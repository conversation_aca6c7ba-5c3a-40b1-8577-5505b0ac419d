<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>LSMinimumSystemVersion</key>
	<string>12.0.0</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>NeoNix Wallet</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIconName</key>
	<string>AppIcon</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ethereum</string>
				<string>neonix</string>
				<string>metamask</string>
				<string>dapp</string>
				<string>wc</string>
				<string>expo-neonix</string>
				<string>expo-metamask</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>twitter</string>
		<string>itms-apps</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>NeoNix Wallet needs Bluetooth access to connect to external devices.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>NeoNix Wallet needs Bluetooth access to connect to external devices.</string>
	<key>NSCameraUsageDescription</key>
	<string>NeoNix Wallet needs camera access to scan QR codes</string>
	<key>NSFaceIDUsageDescription</key>
	<string>$(PRODUCT_NAME) needs to authenticate</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>NeoNix Wallet needs location access to enable Bluetooth connectivity with hardware wallets.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>NeoNix Wallet needs microphone access to record audio</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Allow NeoNix Wallet to save an image to your Photo Library</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Allow NeoNix Wallet to access a images from your Photo Library</string>
	<key>UIAppFonts</key>
	<array>
		<string>Entypo.ttf</string>
		<string>AntDesign.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Metamask.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>MM Poly Regular.otf</string>
		<string>MM Sans Bold.otf</string>
		<string>MM Sans Medium.otf</string>
		<string>MM Sans Regular.otf</string>
		<string>Geist Bold Italic.otf</string>
		<string>Geist Bold.otf</string>
		<string>Geist Medium Italic.otf</string>
		<string>Geist Medium.otf</string>
		<string>Geist Regular Italic.otf</string>
		<string>Geist Regular.otf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen.xib</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>branch_key</key>
	<dict>
		<key>live</key>
		<string>$(MM_BRANCH_KEY_LIVE)</string>
		<key>test</key>
		<string>$(MM_BRANCH_KEY_TEST)</string>
	</dict>
	<key>branch_universal_link_domains</key>
	<array>
		<string>metamask.app.link</string>
		<string>link.metamask.io</string>
		<string>link-test.metamask.io</string>
		<string>metamask-alternate.app.link</string>
		<string>metamask.test.app.link</string>
		<string>metamask-alternate.test.app.link</string>
	</array>
	<key>fox_code</key>
	<string>$(MM_FOX_CODE)</string>
	<key>mixpanel_token</key>
	<string>$(MM_MIXPANEL_TOKEN)</string>
</dict>
</plist>
