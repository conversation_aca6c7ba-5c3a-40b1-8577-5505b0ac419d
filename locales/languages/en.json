{"alert_system": {"alert_modal": {"title": "<PERSON><PERSON>", "checkbox_label": "I have acknowledged the risk and still want to proceed", "got_it_btn": "Got it", "alert_details": "<PERSON><PERSON>"}, "confirm_modal": {"title": "High risk request", "checkbox_label": "I have acknowledged the alert and still want to proceed", "review_alerts": "Review all alerts", "message": "We suggest you reject this request. If you continue, you might put your assets at risk.", "title_blockaid": "Your assets may be at risk", "blockaid": {"message": "If you confirm this request, you could lose your assets. We recommend that you cancel this request.", "message1": "If you confirm this request, you’re allowing a scammer to withdraw and spend your assets. You won’t get them back.", "message2": "You’re sending your assets to a scammer. If you continue, you’ll lose those assets.", "message3": "If you continue, all the assets you’ve listed on OpenSea could be at risk.", "message4": "If you continue, all the assets you’ve listed on Blur could be at risk.", "message5": "You’re interacting with a malicious site. If you continue, you will lose your assets."}}, "inline_alert_label": "<PERSON><PERSON>", "review_alerts": "Review alerts", "review_alert": "Review alert", "upgrade_account": {"title": "Updating your account", "message": "You're updating your account to a smart account. You'll keep the same account address while unlocking faster transactions and lower network fees.", "learn_more": "Learn more"}, "domain_mismatch": {"title": "Suspicious sign-in request", "message": "The site making the request is not the site you’re signing into. This could be an attempt to steal your login credentials."}, "insufficient_balance": {"title": "Insufficient funds", "message": "You do not have enough %{nativeCurrency} in your account to pay for network fees.", "buy_action": "Buy %{nativeCurrency}"}, "signed_or_submitted": {"title": "Transaction in progress", "message": "A previous transaction is still being signed or submitted."}, "signed_or_submitted_perps_deposit": {"title": "Deposit in progress", "message": "You already have a deposit in progress. You'll need to wait for it to go through before depositing again."}, "pending_transaction": {"title": "Pending transaction", "message": "This transaction won't go through until the previous transaction is complete.", "learn_more": "Learn how to cancel or speed up a transaction."}, "batched_unused_approvals": {"title": "Unnecessary permission", "message": "You're giving someone else permission to withdraw your tokens, even though it's not necessary for this transaction."}, "perps_deposit_minimum": {"message": "Minimum $10"}, "insufficient_pay_token_balance": {"message": "Insufficient funds"}, "insufficient_pay_token_balance_fees": {"message": "Insufficient {{symbol}} to cover fees. Add less funds or select a different token to continue.", "title": "Insufficient funds"}, "insufficient_pay_token_native": {"message": "Insufficient {{ticker}} to cover fees. Select a token on a different network to continue."}, "no_pay_token_quotes": {"message": "This payment route isn't available right now. Try changing the amount, network, or token and we'll find the best option."}, "perps_hardware_account": {"title": "<PERSON><PERSON> not supported", "message": "Perps doesn't support hardware wallets.\nSwitch accounts to continue funding."}}, "blockaid_banner": {"approval_farming_description": "If you approve this request, a third party known for scams might take all your assets.", "blur_farming_description": "If you approve this request, someone can steal your assets listed on Blur.", "deceptive_request_title": "This is a deceptive request", "failed_title": "Request may not be safe", "failed_description": "Because of an error, this request was not verified by the security provider. Proceed with caution.", "malicious_domain_description": "You're interacting with a malicious domain. If you approve this request, you might lose your assets.", "other_description": "If you approve this request, you might lose your assets.", "raw_signature_farming_description": "If you approve this request, you might lose your assets.", "seaport_farming_description": "If you approve this request, someone can steal your assets listed on OpenSea.", "see_details": "See details", "does_not_look_right": "Something doesn’t look right?", "report_an_issue": "Report an issue", "suspicious_request_title": "This is a suspicious request", "trade_order_farming_description": "If you approve this request, you might lose your assets.", "transfer_farming_description": "If you approve this request, a third party known for scams will take all your assets.", "before_you_proceed": "Before you proceed", "enable_blockaid_alerts": "To enable this feature we need to set up some security alert. This page will need to stay open while its being set up. This will take less than a minute to complete.", "enable_blockaid_alerts_description": "This page will need to stay open while security alerts are being set up. This process will take less than a minute to complete.", "setting_up_alerts": "Setting up security alerts", "setting_up_alerts_description": "We are going as fast as we can to set up security alerts. Hang in there, we are almost done.", "setup_complete": "Set up complete", "setup_failed": "We could not finish setting up the security alerts. Check your internet connection and try again.", "setup_multiple_failures": "Your internet connection isn’t stable right now, so we couldn’t set up security alerts. Please try again when your connection is restored.", "setup_complete_description": "Security alerts are ready to go. You can close this page and start exploring your wallet", "continue": "Continue", "cancel": "Cancel", "got_it": "Got it", "try_again": "Try Again", "failed": "Something went wrong"}, "date": {"months": {"0": "Jan", "1": "Feb", "2": "Mar", "3": "Apr", "4": "May", "5": "Jun", "6": "Jul", "7": "Aug", "8": "Sept", "9": "Oct", "10": "Nov", "11": "Dec"}, "connector": "at"}, "autocomplete": {"placeholder": "Search by token, site or address", "recents": "Recents", "favorites": "Favorites", "sites": "Sites", "tokens": "Tokens"}, "navigation": {"back": "Back", "close": "Close", "cancel": "Cancel", "info": "Info", "ok": "OK"}, "onboarding": {"title": "Let’s get started!", "sync_desc": "If you’ve already got the MetaMask extension or another wallet, sync or import it to manage your existing assets.", "create_desc": "Set up your first wallet and start exploring decentralized apps.", "import": "Import an existing wallet or create a new one", "import_wallet_button": "Sync or import wallet", "new_to_crypto": "New to crypto?", "start_exploring_now": "Create a new wallet", "unlock": "Unlock", "new_to_metamask": "New to MetaMask?", "already_have_wallet": "Already have a wallet?", "optin_back_title": "Heads up!", "optin_back_desc": "Please agree or disagree to the usage of data analytics.  You can also update this option in settings.", "warning_title": "Warning", "warning_text_1": "Your current wallet and accounts will be", "warning_text_2": "removed", "warning_text_3": "if you proceed.", "warning_text_4": "You can ONLY recover them with your wallet’s Secret Recovery Phrase. MetaMask cannot help you recover it.", "warning_proceed": "Remove wallet & proceed", "warning_cancel": "Cancel", "step1": "Wallet setup", "step2": "Create password", "step3": "Secure wallet", "already_have": "Already have a wallet?", "sync_existing": "Sync your existing MetaMask wallet from the browser extension or import manually.", "scan_title": "Steps to sync with MetaMask extension", "scan": "<PERSON><PERSON>", "scan_step_1": "Open the extension on desktop", "scan_step_2": "Go to Settings > Advanced", "scan_step_3": "Click on “Sync with Mobile”", "scan_step_4": "Scan the QR code to start syncing", "success": "Success", "continue_with_google": "Continue with Google", "continue_with_apple": "Continue with Apple", "or": "or", "import_existing_wallet": "Import existing wallet", "bottom_sheet_title": "Choose an option to continue", "continue_with_srp": "Use Secret Recovery Phrase", "import_srp": "Import using Secret Recovery Phrase", "sign_in_with_google": "Sign in with Google", "sign_in_with_apple": "Sign in with Apple", "your_wallet": "You successfully reset your wallet!", "delete_current": "Reset current wallet", "have_existing_wallet": "I have an existing wallet", "import_using_srp": "Import using Secret Recovery Phrase", "import_using_srp_social_login": "I have an existing wallet", "by_continuing": "By continuing, you agree to MetaMask's", "terms_of_use": "Terms of use", "privacy_notice": "Privacy notice", "and": "and"}, "onboarding_success": {"title": "Keep your Secret Recovery Phrase safe!", "description": "This Secret Recovery Phrase can help you regain access if you ever forget your password or lose access to your login.", "description_bold": "Settings > Security & Privacy.", "description_continued": "you can keep this phrase safe so you never lose access to your money.", "learn_more": "Learn more", "learn_how": "Learn how", "leave_hint": "Leave yourself a hint?", "default_settings": "Default settings", "manage_default_settings": "Manage default settings", "default_settings_footer": "Settings are optimized for ease of use and security.\nChange these at any time.", "no_srp_title": "Reminder set!", "no_srp_description": "If you get locked out of the app or get a new device, you will lose your funds. Be sure to back up your Secret Recovery Phrase in", "import_title": "Your wallet is ready!", "import_description": "If you lose your Secret Recovery Phrase, you won’t be able to use your wallet.", "import_description2": "you can keep this phrase safe so you never lose access to your money.", "import_description_social_login": "You can log in to your wallet anytime with your {{authConnection}} account and password.", "import_description_social_login_2": "If you forget your password, you won’t be able to access your wallet.", "done": "Done", "create_hint": "Create a password hint", "hint_title": "Password hint", "hint_description": "Leave yourself a hint to help remember your password. This hint is stored on your device, and won’t be shared.", "hint_description2": "Remember: If you lose your password, you won’t be able to use your wallet.", "hint_button": "Create hint", "hint_placeholder": "e.g. mom’s home", "hint_saved": "Save", "hint_saved_toast": "Password hint saved", "remind_later": "We’ll remind you later", "remind_later_description": "If you don’t back up your Secret Recovery Phrase, you’ll lose access to your funds if you get locked out of the app or get a new device.", "remind_later_description2": "You can back up your wallets or see your Secret Recovery Phrase in", "setting_security_privacy": "Settings > Security & Privacy"}, "onboarding_carousel": {"title1": "Welcome to MetaMask", "title2": "Manage your digital assets", "title3": "Your gateway to web3", "subtitle1": "Trusted by millions, MetaMask is a secure wallet making the world of web3 accessible to all.", "subtitle2": "Store, spend and send digital assets like tokens, Ethereum, unique collectibles.", "subtitle3": "Login with MetaMask and make transactions to invest, earn, play games, sell and more!", "get_started": "Get started"}, "onboarding_wizard": {"skip_tutorial": "<PERSON><PERSON>", "coachmark": {"action_back": "No, Thanks", "action_next": "Take the tour", "progress_back": "Back", "progress_next": "Got it!"}, "step1": {"title": "Welcome to your new wallet!", "content1": "To use the blockchain, you need a wallet! Some actions cost Ether (ETH).", "content2": "We'll show you how to buy ETH, or you can request some from a friend."}, "step2": {"title": "Your Accounts", "content1": "This is your first account, total value, and its unique public address (0x...).", "content2": "You can create multiple accounts within this wallet by tapping on the profile icon."}, "step3": {"title": "Edit Account Name", "content1": "Why not give your account a memorable and distinct name?", "content2": "Long tap", "content3": "now to edit account name."}, "step4": {"title": "Main Menu", "content1": "You can access Transaction history, Settings, and Support from this menu.", "content2": "You can take more actions with your accounts & access MetaMask settings."}, "step5": {"title": "Explore the Browser", "content1": "You can explore web3 using the browser"}, "step6": {"title": "Search", "content": "Search for sites, or type a URL if you know where you’re headed."}}, "onboarding_wizard_new": {"coachmark": {"action_back": "No thanks", "action_next": "Take the tour", "progress_next": "Got it"}, "step1": {"title": "Welcome to your wallet!", "content1": "On the blockchain, you need a wallet (and maybe some ETH). So let’s take a look at how to use your MetaMask wallet."}, "step2": {"title": "Your accounts", "content1": "This is your account and unique public address (0x...). Create more accounts by tapping the arrow icon."}, "step3": {"title": "Managing your account", "content1": "Edit your account name, view transactions on Etherscan, share your public key, and see your private key by tapping this icon"}, "step4": {"title": "Notifications", "content1": "Stay in the loop on what's happening in your wallet"}, "step5": {"title": "Using your wallet", "content1": "Buy, send, swap, and receive assets by tapping this icon"}, "step6": {"title": "Exploring web3", "content1": "Open the browser by tapping this icon"}, "step7": {"title": "Using the browser", "content1": "Search for sites by keyword or enter a URL. Have fun out there! "}}, "create_wallet": {"title": "Creating your wallet...", "subtitle": "This shouldn’t take long"}, "import_wallet": {"title": "Already a MetaMask user?", "sub_title": "Sync with the extension", "sync_help": "Sync your wallet with the extension", "sync_help_step_one": "1. Open the extension", "sync_help_step_two": "2. Go to Settings > Advanced", "sync_help_step_three": "3. <PERSON><PERSON> on \"Sync with Mobile\"", "sync_help_step_four": "4. <PERSON><PERSON> the QR Code to start syncing", "sync_from_browser_extension_button": "Sync with MetaMask extension", "or": "OR", "import_from_seed_button": "Import using Secret Recovery Phrase"}, "login": {"title": "Welcome Back!", "password": "Password", "password_placeholder": "Enter MetaMask password", "unlock_button": "Unlock", "go_back": "Wallet won't unlock? You can ERASE your current wallet and setup a new one", "forgot_password": "Forgot password?", "cant_proceed": "You can’t proceed till you type the word ‘Delete’. With this action you are opting in to erase your current wallet.", "invalid_password": "Password is incorrect, try again.", "type_delete": "Type ‘%{erase}’ to confirm", "erase_my": "Yes, reset wallet", "cancel": "Cancel", "are_you_sure": "Are you sure?", "your_current_wallet": "Your wallet, accounts, and assets will be", "removed_from": " permanently removed from this app", "this_action": "This action cannot be undone.", "you_can_only": "You can only get this wallet back with the info used when creating it, such as your Secret Recovery Phrase, Google account, or Apple account. MetaMask doesn’t have this info.", "recovery_phrase": "Secret Recovery Phrase ", "metamask_does_not": "MetaMask does not have your Secret Recovery Phrase.", "i_understand": "I understand, continue", "passcode_not_set_error": "Error: Passcode not set.", "wrong_password_error": "Error: <PERSON><PERSON><PERSON> failed", "wrong_password_error_android": "Error: error:1e000065:Cipher functions:OPENSSL_internal:BAD_DECRYPT", "vault_error": "Error: <PERSON><PERSON> unlock without a previous vault.", "clean_vault_error": "MetaMask encountered an error due to reaching a storage limit. The local data has been corrupted. Please reinstall MetaMask and restore with your Secret Recovery Phrase.", "seedless_password_outdated": "Your password was changed recently.", "seedless_password_outdated_modal_title": "Your password was changed", "seedless_password_outdated_modal_content": "Your password was recently changed, so you need to enter your new password to stay logged into MetaMask.", "seedless_password_outdated_modal_confirm": "Continue", "no_internet_connection": "Seedless account recovery requires internet connection.", "seedless_controller_error_prompt_title": "Something went wrong", "seedless_controller_error_prompt_description": "An issue occurred while unlocking. Re-login with Google and your MetaMask password.", "seedless_controller_error_prompt_primary_button_label": "Log in", "security_alert_title": "Security Alert", "security_alert_desc": "In order to proceed, you need to turn Passcode on or any biometrics authentication method supported in your device (FaceID, TouchID or Fingerprint)", "too_many_attempts": "Too many attempts. Please try again in {{remainingTime}}", "apple_button": "Sign in with Apple", "google_button": "Sign in with Google", "other_methods": "Use a different login method", "forgot_password_desc": "Forgot your password?", "forgot_password_desc_2": "MetaMask can’t recover your password for you.", "forgot_password_point_1": "If you’re logged into MetaMask on a device with", "forgot_password_point_1_bold": "biometrics turned on", "forgot_password_point_1_1": "(like Face ID), you can reset your password there.", "forgot_password_point_2": "If you have your", "forgot_password_point_2_bold": "Secret Recovery Phrase,", "forgot_password_point_2_1": "you can reset your current wallet and reimport using Secret Recovery Phrase.", "reset_wallet": "Reset wallet", "reset_wallet_desc": "Your wallet data will be", "reset_wallet_desc_bold": "permanently erased", "reset_wallet_desc_2": "from MetaMask on this device. This can’t be undone.", "reset_wallet_desc_login": "To restore your wallet, you can use your Secret Recovery Phrase, or your Google or Apple account password. MetaMask doesn’t have this information.", "reset_wallet_desc_srp": "To restore your wallet, make sure you have your Secret Recovery Phrase. MetaMask doesn’t have this information."}, "connect_hardware": {"title_select_hardware": "Connect a hardware wallet", "select_hardware": "Select a hardware wallet you'd like to use"}, "enter_password": {"title": "Enter your password", "desc": "Please enter your password in order to continue", "password": "Password", "confirm_button": "Confirm", "error": "Error"}, "choose_password": {"title": "MetaMask password", "description": "Unlocks MetaMask on this device only.", "description_social_login": "Use this for wallet recovery on all devices.", "description_social_login_update": "If you lose this password, you’ll lose access to your wallet on all devices. Store it somewhere safe,", "description_social_login_update_bold": "MetaMask can't reset it.", "subtitle": "This password will unlock your MetaMask wallet only on this device.", "password": "Create password", "confirm_password": "Confirm password", "create_button": "Create password", "import_with_seed_phrase": "Import with Secret Recovery Phrase", "password_length_error": "The password needs to be at least 8 characters long", "password_dont_match": "Passwords don't match", "password_strength": "Password strength:", "strength_weak": "Weak", "strength_good": "Good", "strength_strong": "Strong", "show": "Show", "hide": "<PERSON>de", "seed_phrase": "Secret Recovery Phrase", "must_be_at_least": "Must be at least {{number}} characters", "remember_me": "Remember me", "security_alert_title": "Security Alert", "security_alert_message": "In order to proceed, you need to turn Passcode on or any biometrics authentication method supported in your device (FaceID, TouchID or Fingerprint)", "i_understand": "I understand that MetaMask cannot recover this password for me.", "learn_more": "Learn more.", "secure": "Secure wallet", "confirm": "Confirm Secret Recovery Phrase", "disable_biometric_error": "You have disabled biometrics for the app. Update Remember Me settings and try again.", "create_password_cta": "Create password", "steps": "Step {{currentStep}} of {{totalSteps}}", "password_error": "Passwords don’t match.", "marketing_opt_in_description": "Get product updates, tips, and news including by email. We may use your interactions to improve what we share.", "loose_password_description": "If I lose this password, MetaMask can’t reset it."}, "reset_password": {"title": "Change password", "subtitle": "This password will unlock your MetaMask wallet only on this device.", "password": "New password", "confirm_password": "Confirm password", "reset_button": "Reset password", "import_with_seed_phrase": "Import with Secret Recovery Phrase", "password_length_error": "The password needs to be at least 8 characters long", "password_dont_match": "Passwords don't match", "password_strength": "Password strength:", "strength_weak": "Weak", "strength_good": "Good", "strength_strong": "Strong", "show": "Show", "hide": "<PERSON>de", "seed_phrase": "Secret Recovery Phrase", "must_be_at_least": "Must be at least {{number}} characters", "remember_me": "Remember me", "security_alert_title": "Security Alert", "security_alert_message": "In order to proceed, you need to turn Passcode on or any biometrics authentication method supported in your device (FaceID, TouchID or Fingerprint)", "i_understand": "I understand that MetaMask cannot recover this password for me.", "learn_more": "Learn more.", "secure": "Secure wallet", "confirm": "Confirm Secret Recovery Phrase", "password_updated": "New password saved", "successfully_changed": "Your password has been successfully changed", "new_password_placeholder": "Use at least 8 characters", "confirm_password_placeholder": "Re-enter your password", "confirm_btn": "Save", "warning_password_change_title": "Are you sure?", "warning_password_change_description": "Changing your password here will lock MetaMask on other devices you’re using. You’ll need to log in again with your new password", "warning_password_change_button": "Confirm", "warning_password_cancel_button": "Cancel", "changing_password": "Changing password...", "changing_password_subtitle": "This shouldn’t take long", "checkbox_forgot_password": "If I forget this password, I’ll lose access to my wallet permanently. MetaMask can’t reset it for me.", "seedless_change_password_error_modal_title": "Change password failed", "seedless_change_password_error_modal_content": "We were unable to change your password. Please try again.", "seedless_change_password_error_modal_confirm": "Try again"}, "import_from_seed": {"title": "Import a wallet", "seed_phrase_placeholder": "Enter your Secret Recovery Phrase", "create_new_password": "Create password", "confirm_password": "Confirm password", "import_button": "IMPORT", "cancel_button": "Cancel", "password_length_error": "The password needs to be at least 8 characters long", "password_dont_match": "Passwords don't match", "seed_phrase_requirements": "Secret Recovery Phrases contain 12, 15, 18, 21, or 24 words", "invalid_seed_phrase": "Secret Recovery Phrase not found.", "error": "Error", "invalid_qr_code_title": "Invalid QR Code", "invalid_qr_code_message": "This QR code doesn't represent a valid Secret Recovery Phrase", "enter_your_secret_recovery_phrase": "Enter your Secret Recovery Phrase", "metamask_password": "MetaMask password", "metamask_password_description": "Unlocks MetaMask on this device only.", "seed_phrase_required": "Secret Recovery Phrase is required", "re_enter_password": "Re-enter your password", "use_at_least_8_characters": "Use at least 8 characters", "unlock_with_face_id": "Unlock with Face ID?", "learn_more": "If I forget this password, <PERSON><PERSON><PERSON><PERSON> can't recover it for me.", "learn_more_social_login": "If I forget this password, I’ll lose access to my wallet permanently. MetaMask can’t reset it for me.", "seed_phrase_length_error": "Secret Recovery Phrase must be 12, 15, 18, 21, or 24 words", "continue": "Continue", "clear_all": "Clear all", "show_all": "Show all", "paste": "Paste", "hide_all": "Hide all", "srp": "Secret Recovery Phrase", "srp_placeholder": "Add a space between each word and make sure no one is watching.", "learn_more_link": "Learn more", "import_create_password_cta": "Create password", "pass_flow": "Continue with Flow", "steps": "Step {{currentStep}} of {{totalSteps}}", "password_error": "Passwords don’t match. Try again.", "spellcheck_error": "Use only lowercase letters, check your spelling, and put the words in the original order.", "enter_strong_password": "Enter a strong password"}, "bottom_tab_bar": {"dapps": "ÐApps", "wallet": "Wallet", "transfer": "Transfer"}, "bottom_nav": {"home": "Home", "browser": "Browser", "activity": "Activity", "trade": "Trade", "settings": "Settings", "rewards": "Rewards"}, "drawer": {"send_button": "Send", "receive_button": "Add funds", "coming_soon": "Coming soon...", "wallet": "Wallet", "transaction_activity": "Activity", "request_feature": "Request a Feature", "submit_feedback_message": "Choose the type of feedback to send.", "submit_bug": "Bug Report", "submit_general_feedback": "General", "share_address": "Share my Public Address", "view_in_etherscan": "View on Etherscan", "view_in": "View on", "browser": "Browser", "settings": "Settings", "settings_warning": "Wallet unprotected", "settings_warning_short": "Unprotected", "help": "Support", "lock": "Lock", "lock_title": "Do you really want to lock your wallet?", "lock_ok": "YES", "lock_cancel": "NO", "feedback": "<PERSON><PERSON><PERSON>", "metamask_support": "MetaMask Support", "public_address": "Public Address"}, "send": {"available": "available", "invalid_value": "Invalid value", "insufficient_funds": "Insufficient funds", "continue": "Continue", "unit": "unit", "units": "units", "next": "Next", "title": "Send", "deeplink_failure": "Something went wrong. Please try again", "warn_network_change": "Network changed to ", "send_to": "Send to", "amount": "Amount", "confirm": "Confirm", "paste": "Paste", "no_assets_available": "No assets available to send", "clear": "Clear", "to": "To", "enter_address_to_send_to": "Enter address to send to", "accounts": "Accounts", "contacts": "Contacts", "no_contacts_found": "No contacts found", "review": "Review", "all_networks": "All", "no_tokens_match_filters": "No tokens match your filters", "clear_filters": "Clear filters", "no_tokens_available": "No tokens available", "sign": "Sign", "network_not_found_title": "Network not found", "network_not_found_description": "Network with chain id {{chain_id}} not found in your wallet. Please add the network first.", "network_missing_id": "Missing chain id.", "search_tokens_and_nfts": "Search tokens and NFTs", "tokens": "Tokens", "nfts": "NFTs", "show_more_nfts": "Show more NFTs", "token_contract_warning": "This address is a token contract address. If you send tokens to this address, you will lose them.", "invisible_character_error": "We detected an invisible character in the ENS name. Check the ENS name to avoid a potential scam.", "could_not_resolve_name": "Couldn't resolve name"}, "deposit": {"title": "<PERSON><PERSON><PERSON><PERSON>", "selectRegion": "Select Region", "chooseYourRegionToContinue": "Choose your region to continue", "buildQuote": {"payWith": "Pay with", "unexpectedError": "An unexpected error occurred.", "quoteFetchError": "Failed to fetch quote.", "kycFormsFetchError": "Failed to fetch KYC forms.", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "token_modal": {"select_a_token": "Select a Token", "select_token": "Select token", "search_by_name_or_address": "Search token by name or address", "no_tokens_found": "No tokens match \"{{searchString}}\""}, "networks_filter_bar": {"all_networks": "All networks"}, "networks_filter_selector": {"select_network": "Select network", "select_all": "Select all", "deselect_all": "Deselect all", "apply": "Apply"}, "configuration_modal": {"title": "Options", "view_order_history": "View order history", "contact_support": "Contact support", "log_out": "Log out", "logged_out_success": "Successfully logged out", "error_sdk_not_initialized": "SDK not initialized", "logged_out_error": "Error logging out"}, "region_modal": {"select_a_region": "Select a region", "search_by_country": "Search by country", "no_regions_found": "No regions match \"{{searchString}}\""}, "state_modal": {"select_a_state": "Select a state", "search_by_state": "Search by state", "no_state_results": "No states match \"{{searchString}}\""}, "payment_modal": {"select_a_payment_method": "Select a Payment Method"}, "payment_duration": {"instant": "Instant", "1_to_2_days": "1 to 2 days"}, "unsupported_region_modal": {"title": "Region not supported", "location_prefix": "It looks like you're in:", "description": "We're working hard to expand coverage to your region. In the meantime, there may be other ways for you to get crypto.", "change_region": "Change region", "buy_crypto": "Buy Crypto"}, "unsupported_state_modal": {"title": "Region not supported", "location_prefix": "You've selected:", "description": "We're working hard to expand coverage to your region. In the meantime, there are other ways for you to get crypto.", "change_state": "Change region", "try_another_option": "Try another option"}, "incompatible_token_acount_modal": {"title": "Switch your account", "description": "The {{networkName}} account you’re on doesn't support the selected token. Switch your account or token to continue.", "cta": "I understand"}, "ssn_info_modal": {"title": "Why we ask", "description": "U.S. laws require Transak to check your identity before changing dollars into crypto. This means U.S. customers need to provide a Social Security number."}, "get_started": {"navbar_title": "<PERSON><PERSON><PERSON><PERSON>", "title": "Starting is easy with USDC", "bullet_1_title": "Value-backed by the US dollar", "bullet_1_description": "USDC is a digital dollar, backed by U.S. dollars, so you always know what it’s worth.", "bullet_2_title": "Low volatility", "bullet_2_description": "USDC is designed to stay stable, making it easier to plan, save, and spend.", "bullet_3_title": "Faster than banks", "bullet_3_description": "Send and receive in minutes, no waiting for bank hours or international delays.", "button": "Get started"}, "enter_email": {"navbar_title": "Verify your identity", "title": "Enter your email", "description": "We'll email you a six-digit verification code to securely log in or create an account with Transak.", "input_placeholder": "<EMAIL>", "submit_button": "Send email", "loading": "Sending email...", "validation_error": "Please enter a valid email address", "error": "An error occurred while sending the email"}, "otp_code": {"navbar_title": "Verify your identity", "title": "Enter six-digit code", "description": "Enter the code we sent to {{email}}. If you don't see it, check your spam folder.", "submit_button": "Submit", "loading": "Verifying code...", "invalid_code_error": "Invalid code", "validation_error": "Please enter a valid code", "need_help": "Need help?", "resend_code_description": "Didn't receive the code?", "resend_code_error": "Error resending code.", "resend_code_button": "Resend it", "resend_cooldown": "Resend code in {{seconds}} seconds", "contact_support": "Contact support", "error": "An error occurred while verifying the code"}, "verify_identity": {"title": "Verify your identity", "navbar_title": "<PERSON><PERSON><PERSON><PERSON>", "description_1": "To deposit cash for the first time, your identity must be verified.", "description_2_transak": "Transak", "description_2_rest": " will facilitate your deposit and your data is sent directly to Transak. We do not process the data you share.", "description_3_part1": "Check out our ", "description_3_privacy_policy": "privacy policy", "description_3_part2": " to learn more.", "agreement_text_part1": "By clicking the button below, you agree to ", "agreement_text_transak_terms": "Transak's Terms of Use", "agreement_text_and": " and ", "agreement_text_privacy_policy": "Privacy Policy", "agreement_text_part2": ".", "button": "Agree and continue"}, "additional_verification": {"title": "Additional Verification", "paragraph_1": "For larger deposits, you’ll need a valid ID (like a driver’s license) and a real-time selfie.", "paragraph_2": "In order to complete your verification, you’ll need to enable access to your camera.", "button": "Continue"}, "basic_info": {"navbar_title": "Verify your identity", "title": "Enter your basic info", "continue": "Continue", "subtitle": "Next, we need some basic information about you.", "first_name": "First name", "last_name": "Last name", "phone_number": "Phone number", "date_of_birth": "Date of birth", "social_security_number": "Social security number (SSN)", "enter_phone_number": "Enter phone number", "select_region": "Select region", "first_name_required": "First name is required", "first_name_invalid": "Please enter a valid first name", "last_name_required": "Last name is required", "last_name_invalid": "Please enter a valid last name", "mobile_number_required": "Phone number is required", "mobile_number_invalid": "Please enter a valid phone number", "dob_required": "Date of birth is required", "dob_invalid": "Please enter a valid date of birth", "ssn_required": "Social security number is required", "unexpected_error": "An unexpected error occurred. Please try again."}, "enter_address": {"navbar_title": "Verify your identity", "title": "Enter your address", "subtitle": "Use your most recent permanent address.", "continue": "Continue", "address_line_1": "Address line 1", "address_line_2": "Address line 2 (optional)", "state": "State/Region", "city": "City", "postal_code": "Postal/Zip Code", "country": "Country", "select_state": "Select state", "address_line_1_required": "Address line 1 is required", "address_line_1_invalid": "Please enter a valid address", "address_line_2_invalid": "Please enter a valid address", "city_required": "City is required", "city_invalid": "Please enter a valid city", "state_required": "State/Region is required", "state_invalid": "Please enter a valid state", "postal_code_required": "Postal/Zip Code is required", "postal_code_invalid": "Please enter a valid postal code", "unexpected_error": "Unexpected error."}, "privacy_section": {"transak": "Transak encrypts and stores this information.", "metamask": "MetaMask never receives or uses your data."}, "kyc_processing": {"navbar_title": "Verify your identity", "heading": "Hang tight...", "description": "This should only take about 2 minutes.", "error_heading": "We were unable to verify your identity.", "error_description": "Try uploading your documents again and resubmit for verification.", "error_button": "Retry verification", "success_heading": "You're verified", "success_description": "Now you can complete your deposit.", "success_button": "Complete your order"}, "provider_webview": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "kyc_webview": {"title": "Verifying your identity", "webview_received_error": "WebView received error status code: {{code}}"}, "order_processing": {"title": "Processing deposit", "button": "Close", "description": "Card purchases typically take a few minutes", "bank_transfer_description": "Bank transfers typically take a few business days", "success_title": "Deposit complete", "success_description": "Your deposit of {{amount}} {{currency}} was successful!", "error_title": "Depo<PERSON><PERSON> failed", "error_description": "We were unable to complete your deposit", "cancel_order_description": "You requested a cancellation of your deposit.", "error_button": "Try again", "contact_support_button": "Contact support", "account": "Account", "network": "Network", "order_id": "Order ID", "fees": "Fees", "total": "Total", "view_order_details_in_transak": "View order details in Transak", "no_order_found": "No order found", "back_to_wallet": "Back to Wallet", "cancel_order_button": "Cancel order"}, "order_details": {"title": "Deposit Order", "error_title": "There was an error with your deposit order", "error_message": "An unexpected error occurred."}, "bank_details": {"navbar_title": "{{paymentMethod}} bank transfer", "button": "Confirm transfer", "button_cancel": "Cancel order", "main_title": "To complete your order", "main_content_1": "Initiate a transfer from your preferred bank using the information below.", "main_content_2": "Once you’re finished, come back and click “Confirm transfer” to confirm.", "show_bank_info": "Show bank information", "hide_bank_info": "Hide bank information", "transfer_amount": "Transfer amount", "account_holder_name": "Account holder name", "beneficiary_name": "Beneficiary name", "routing_number": "Routing number", "account_number": "Account number", "iban": "IBAN", "bic": "BIC", "account_type": "Account type", "bank_name": "Bank name", "beneficiary_address": "Beneficiary address", "bank_address": "Bank address", "info_banner_text": "You should see '{{accountHolderName}}' as the account holder. This helps your bank process the transfer.", "error_message": "We were unable to confirm your bank transfer. Please try again.", "cancel_order_error": "We were unable to cancel your order. Please try again."}, "webview_modal": {"error": "Webview received error: %{code}"}, "notifications": {"deposit_failed_title": "Deposit of {{currency}} has failed! Please try again, sorry for the inconvenience!", "deposit_failed_description": "Verify your payment method and card support", "deposit_cancelled_title": "Your deposit was cancelled", "deposit_cancelled_description": "You requested a cancellation of your deposit.", "deposit_completed_title": "Your deposit of {{amount}} {{currency}} was successful!", "deposit_completed_description": "Your {{currency}} is now available in your wallet", "deposit_pending_title": "Processing your deposit of {{currency}}", "deposit_pending_description": "This should only take a few minutes..."}, "error_view": {"title": "There was an error", "description": "There was an error processing your deposit. Please contact support if the problem persists.", "try_again": "Try again"}}, "perps": {"title": "Perps", "perps_trading": "Perps Trading", "perp_account_balance": "Perp account balance", "manage_balance": "Manage Balance", "total_balance": "Total Balance", "available_balance": "Available balance", "margin_used": "Margin Used", "gtm_content": {"title": "PERPS ARE HERE", "title_description": "Long or short tokens with up to 40x leverage. Fund your account with any EVM token in one click.", "not_now": "Not now", "try_now": "Get started"}, "unrealized_pnl": "Unrealized PnL", "withdraw": "Withdraw", "refresh_balance": "Refresh Balance", "add_funds": "Add funds", "your_positions": "Your positions", "loading_positions": "Loading positions...", "refreshing_positions": "Refreshing positions...", "no_open_orders": "No open orders", "deposit": {"title": "Amount to deposit", "get_usdc_hyperliquid": "Get USDC • Hyperliquid", "insufficient_funds": "Insufficient funds", "enter_amount": "Enter amount", "fetching_quote": "Fetching quote", "submitting": "Submitting transaction", "get_usdc": "Get USDC", "network_fee": "Network fee", "estimated_time": "Estimated time", "rate": "Rate", "slippage": "Slippage", "slippage_info": "If the price changes between the time your order is placed and confirmed it's called slippage. Your transaction will automatically cancel if slippage exceeds the tolerance you set here.", "slippage_auto": "Auto", "apply": "Apply", "max_button": "Max", "done_button": "Done", "metamask_fee": "MetaMask fee", "metamask_fee_tooltip": "MetaMask does not charge any fees for deposits to Hyperliquid", "minimum_deposit_error": "Minimum deposit amount is {{amount}} USDC", "processing_title": "Processing deposit", "preview": {"title": "Deposit Preview"}, "processing": {"title": "Processing Deposit"}, "deposit_completed": "Deposit completed successfully!", "deposit_failed": "Deposit Failed", "retry_deposit": "Retry Deposit", "go_back": "Go Back", "view_balance": "View Balance", "calculating_fee": "Calculating...", "quote_expired_modal": {"title": "Quote Expired", "description": "The quote for your deposit has expired after {{refreshRate}} seconds. Get a new quote to continue.", "get_new_quote": "Get New Quote"}, "steps": {"preparing": "Preparing deposit...", "swapping": "Swapping {{token}} to USDC", "bridging": "Bridging to Hyperliquid", "depositing": "Depositing into perps account", "depositing_direct": "Transferring USDC directly to your HyperLiquid account..."}, "step_descriptions": {"preparing": "Preparing your deposit transaction...", "swapping": "Converting your tokens to USDC for deposit...", "bridging": "Moving USDC to Arbitrum network...", "depositing": "Transferring USDC to your HyperLiquid account...", "success": "Successfully deposited {{amount}} USDC to your HyperLiquid account", "error": "Something went wrong during the deposit process. Please try again."}, "quote_fetch_error": "Failed to fetch deposit quote. Please try again.", "bridge_quote_timeout": "Unable to fetch bridge quote. Please try again or select a different token.", "no_quotes_available": "No routes available for this swap. Try a different token or amount.", "success": {"title": "Deposit Successful!", "description": "Your USDC has been successfully deposited to your HyperLiquid trading account", "amount": "Amount", "processing_time": "Processing Time", "status": "Status", "completed": "Completed", "view_balance": "View Balance", "view_transaction": "View Transaction"}, "success_toast": "Your Perps account was funded", "success_message": "{{amount}} available to trade", "funds_are_ready_to_trade": "Funds are ready to trade", "error_toast": "Transaction failed", "error_generic": "Funds have been returned to you", "in_progress": "Adding funds to Perps", "estimated_processing_time": "Est. {{time}}", "funds_available_momentarily": "Funds will be available momentarily", "your_funds_are_available_to_trade": "Your funds are available to trade", "track": "Track"}, "withdrawal": {"title": "Withdraw", "insufficient_funds": "Insufficient funds", "enter_amount": "Enter amount", "review": "Review withdrawal", "withdraw": "Withdraw", "withdraw_usdc": "Withdraw USDC", "minimum_amount_error": "Minimum withdrawal amount is ${{amount}}", "amount_too_low": "Amount must be greater than ${{minAmount}} to cover fees", "confirm": "Confirm withdrawal", "processing_title": "<PERSON><PERSON><PERSON> initiated", "eta_will_be_shared_shortly": "ETA will be shared shortly", "success_title": "<PERSON><PERSON><PERSON> successful", "success_description": "Your {{amount}} USDC has been withdrawn from Hyperliquid", "network_fee": "Network fee", "metamask_fee": "MetaMask fee", "total_fees": "Total fees", "receiving_amount": "You will receive", "estimated_time": "Estimated time", "done": "Done", "initiated": "<PERSON><PERSON><PERSON> initiated", "wait_time_message": "Your funds should arrive within 5 minutes", "submitting": "Submitting...", "error": "<PERSON><PERSON><PERSON> failed", "success_toast": "<PERSON><PERSON><PERSON> confirmed", "success_toast_description": "You'll receive {{amount}} {{symbol}} on {{networkName}} within 5 minutes", "bridge_info": "HyperLiquid validators are processing your withdrawal", "error_generic": "An error occurred during withdrawal", "invalid_amount": "Please enter a valid amount", "max": "Max", "percentage_10": "10%", "percentage_25": "25%", "available_balance": "Available perps balance: {{amount}}", "receive": "Receive", "provider_fee": "Provider fee", "you_will_receive": "You'll receive", "continue": "Continue", "funds_received": "{{amount}} {{symbol}} received"}, "quote": {"network_fee": "Network fee", "estimated_time": "Estimated time", "rate": "Rate", "metamask_fee": "MetaMask fee", "metamask_fee_tooltip": "MetaMask does not charge any fees for Hyperliquid transactions", "metamask_fee_tooltip_deposit": "MetaMask does not charge any fees for deposits to Hyperliquid", "metamask_fee_tooltip_withdrawal": "MetaMask does not charge any fees for withdrawals from Hyperliquid"}, "order": {"title": "New Order", "leverage": "Leverage", "limit_price": "Limit price", "enter_price": "Enter price", "trigger_price": "Trigger price", "liquidation_price": "Liquidation price", "fees": "Fees", "market": "Market", "limit": "Limit", "open_orders": "Orders", "max": "max", "cancel_order": "Cancel order", "filled": "filled", "reduce_only": "Reduce only", "yes": "Yes", "status": {"open": "Open", "filled": "Filled", "canceled": "Canceled", "rejected": "Rejected"}, "validation": {"failed": "Order validation failed", "amount_required": "Order amount must be greater than 0", "minimum_amount": "Minimum order size is ${{amount}}", "insufficient_funds": "Insufficient funds", "insufficient_balance": "Insufficient balance. Required: ${{required}}, Available: ${{available}}", "invalid_leverage": "Leverage must be between {{min}}x and {{max}}x", "high_leverage_warning": "High leverage increases liquidation risk", "invalid_take_profit": "Take profit must be {{direction}} current price for {{positionType}} positions", "invalid_stop_loss": "Stop loss must be {{direction}} current price for {{positionType}} positions", "liquidation_warning": "Position is close to liquidation price", "limit_price_required": "Please set a limit price for limit orders", "please_set_a_limit_price": "Please set a limit price", "limit_price_must_be_set_before_configuring_tpsl": "Limit price must be set before configuring TP/SL", "only_hyperliquid_usdc": "Only USDC on Hyperliquid is currently supported for payment", "limit_price_far_warning": "Limit price is far from current market price"}, "error": {"placement_failed": "Order placement failed", "network_error": "Network error", "unknown": "Unknown error occurred", "dismiss": "<PERSON><PERSON><PERSON>", "invalid_asset": "Invalid asset", "go_back": "Go Back", "asset_not_tradable": "{{asset}} is not a tradable asset"}, "off": "Off", "estimated_execution_time": "Estimated execution time", "one_to_three_seconds": "1 to 3 seconds", "margin": "<PERSON><PERSON>", "take_profit": "Take profit", "stop_loss": "Stop loss", "tp_sl": "TP/SL", "button": {"long": "Long {{asset}}", "short": "Short {{asset}}"}, "tpsl_modal": {"title": "Take profit & Stop loss", "save": "Save", "current_price": "Current price: {{price}}", "on": "ON", "off": "OFF", "take_profit_helper": "Close position when price reaches this level", "stop_loss_helper": "Limit losses by closing position at this price"}, "limit_price_modal": {"title": "Set limit price", "set": "Set", "market_price": "Market price: {{price}}", "market": "Market", "current_price": "Current price", "ask_price": "Ask price", "bid_price": "Bid price", "difference_from_market": "Difference from market:", "limit_price_above": "Limit price is above current price", "limit_price_below": "Limit price is below current price"}, "leverage_modal": {"title": "Leverage", "confirm": "Confirm", "entry_price": "Entry Price", "current_price": "Current price", "liquidation_price": "Liquidation Price", "liquidation_distance": "Liquidation Distance"}, "type": {"title": "Order Type", "market": {"title": "Market", "description": "Execute immediately at current market price"}, "limit": {"title": "Limit", "description": "Execute only at your specified price or better"}}, "success": {"title": "Order Placed Successfully", "subtitle": "Your {{direction}} position for {{asset}} has been created", "asset": "<PERSON><PERSON>", "direction": "Direction", "amount": "Amount", "orderId": "Order ID", "viewPositions": "View Positions", "placeAnother": "Place Another Order", "backToPerps": "Back to Perps"}, "submitted": "Order Submitted", "confirmed": "Order Confirmed", "cancelling_order": "Cancelling order", "cancelling_order_subtitle": "{{direction}} {{amount}} {{assetSymbol}}", "order_cancelled": "Order cancelled", "failed_to_cancel_order": "Failed to cancel order", "funds_have_been_returned_to_you": "Funds have been returned to you", "funds_are_available_to_trade": "Funds are available to trade", "close_order_still_active": "Close order still active", "order_submitted": "Order submitted", "order_filled": "Order filled", "order_placed": "Order placed", "order_placement_subtitle": "{{direction}} {{amount}} {{assetSymbol}}", "order_failed": "Order failed", "your_funds_have_been_returned_to_you": "Your funds have been returned to you"}, "close_position": {"title": "Close position", "button": "Close position", "closing": "Closing position...", "position_close_order_placed": "Placed order to close position", "partially_closing_position": "Partially closing position", "partial_close_submitted": "Partial close submitted", "position_partially_closed": "Position partially closed", "closing_position": "Closing position", "limit_close_order_cancelled": "Limit close order cancelled", "closing_position_subtitle": "{{direction}} {{amount}} {{assetSymbol}}", "your_funds_will_be_available_momentarily": "Your funds will be available momentarily", "cancel": "Cancel", "margin": "<PERSON><PERSON>", "includes_pnl": "includes P&L", "pnl": "PnL", "estimated_pnl": "Estimated PnL", "fees": "Fees", "receive": "You'll receive", "you_receive": "You'll receive", "select_amount": "Select amount to close", "error_unknown": "Failed to close position", "success_title": "Position Closed Successfully", "position_closed": "Position closed", "funds_are_available_to_trade": "Funds are available to trade", "error_title": "Close Position Failed", "fox_points_earned": "{{points}} Fox Points earned!", "minimum_remaining_warning": "Remaining position must be at least ${{minimum}}. Current: ${{remaining}}", "minimum_remaining_error": "Cannot partial close: remaining position (${{remaining}}) would be below minimum order size (${{minimum}}). Please close 100% instead.", "must_close_full_below_minimum": "Position value is below $10. You must close 100% of the position.", "negative_receive_amount": "The fees exceed your position value", "no_amount_selected": "Please select an amount to close", "order_type_reverted_to_market_order": "Changed to market order", "you_need_set_price_limit_order": "You need to set a price for a limit order."}, "tpsl": {"title": "Take profit and stop loss", "description": "Pick a percentage gain or loss, or enter a custom trigger price to automatically close your position.", "set": "Set", "updating": "Updating...", "off": "Off", "current_price": "Current price", "leverage": "Leverage", "margin": "<PERSON><PERSON>", "liquidation_price": "Liquidation price", "take_profit_long": "Take profit", "take_profit_short": "Take profit", "stop_loss_long": "Stop loss", "stop_loss_short": "Stop loss", "trigger_price_placeholder": "Trigger price", "profit_percent_placeholder": "Profit", "loss_percent_placeholder": "Loss", "profit_roe_placeholder": "% Profit", "loss_roe_placeholder": "% Loss", "usd_label": "($)", "take_profit_invalid_price": "Take profit must be {{direction}} {{priceType}} price", "stop_loss_invalid_price": "Stop loss must be {{direction}} {{priceType}} price", "stop_loss_beyond_liquidation_error": "Stop loss must be {{direction}} liquidation price", "stop_loss_order_view_warning": "Stop loss is {{direction}} liquidation price", "above": "above", "below": "below"}, "token_selector": {"no_tokens": "No tokens available"}, "errors": {"minimumDeposit": "Minimum deposit amount is {{amount}} USDC", "tokenNotSupported": "Token {{token}} not supported for deposits", "unknownError": "Unknown error occurred", "clientNotInitialized": "HyperLiquid SDK clients not properly initialized", "exchangeClientNotAvailable": "ExchangeClient not available after initialization", "infoClientNotAvailable": "InfoClient not available after initialization", "subscriptionClientNotInitialized": "SubscriptionClient is not initialized", "failedToSubscribePosition": "Failed to subscribe to position updates", "failedToUnsubscribePosition": "Failed to unsubscribe from position updates", "failedToSubscribeOrderFill": "Failed to subscribe to order fill updates", "failedToUnsubscribeOrderFill": "Failed to unsubscribe from order fill updates", "failedToEstablishAllMids": "Failed to establish global allMids subscription", "failedToEstablishMarketData": "Failed to establish market data subscription for {{symbol}}", "failed_to_toggle_network": "Failed to toggle network", "noAccountSelected": "No account selected", "unsupportedMethod": "Unsupported method: {{method}}", "invalidAddressFormat": "Invalid address format: {{address}}", "depositValidation": {"assetIdRequired": "AssetId is required for deposit validation", "amountRequired": "Amount is required and must be greater than 0", "amountPositive": "Amount must be a positive number"}, "withdrawValidation": {"missingParams": "Missing required parameters for withdrawal", "assetIdRequired": "assetId is required for withdrawals", "amountRequired": "amount is required for withdrawals", "amountPositive": "Amount must be a positive number", "invalidDestination": "Invalid destination address format: {{address}}", "insufficientBalance": "Insufficient balance. Available: {{available}}, Requested: {{requested}}", "assetNotSupported": "Asset {{assetId}} is not supported for withdrawals. Supported assets: {{supportedAssets}}"}, "orderValidation": {"coinRequired": "Coin is required for orders", "sizePositive": "Size must be a positive number", "pricePositive": "Price must be a positive number if provided", "unknownCoin": "Unknown coin: {{coin}}"}, "networkToggleFailed": "Failed to toggle network: {{error}}", "accountBalanceFailed": "Failed to get account balance: {{error}}", "positionsFailed": "Failed to get positions", "accountStateFailed": "Failed to get account state", "marketsFailed": "Failed to get markets", "bridgeContractNotFound": "Unable to get HyperLiquid bridge contract address", "providerNotAvailable": "Provider {{providerId}} not available", "withdrawFailed": "Failed to withdraw", "connectionRequired": "usePerpsConnection must be used within a PerpsConnectionProvider", "assetMappingFailed": "Failed to build asset mapping", "depositFailed": "Depo<PERSON><PERSON> failed", "orderLeverageReductionFailed": "You cannot reduce your leverage", "connectionFailed": {"title": "Perps is temporarily offline", "description": "We're working to get it back online soon.", "retry": "Retry", "go_back": "Go Back"}, "networkError": {"title": "Network Error", "description": "There was a problem connecting to the network. Please try again.", "retry": "Try Again"}, "unknown": {"title": "Something Went Wrong", "description": "An unexpected error occurred. Please try again later.", "retry": "Retry"}}, "position": {"title": "Positions", "card": {"entry_price": "Entry price", "funding_cost": "Funding", "liquidation_price": "Liq<PERSON>", "take_profit": "Take profit", "stop_loss": "Stop loss", "margin": "<PERSON><PERSON>", "not_set": "Not set", "edit_tpsl": "Edit TP/SL", "close_position": "Close position", "tpsl_count_multiple": "{{count}} orders", "tpsl_count_single": "{{count}} order"}, "list": {"loading": "Loading positions...", "error_title": "Error Loading Positions", "empty_title": "No Open Positions", "empty_description": "You don't have any open positions yet.\nStart trading to see your positions here.", "first_time_title": "Perps", "first_time_description": "Predict price moves with up to 40x leverage.", "start_trading": "Start trading", "start_new_trade": "Start a new trade", "open_positions": "Open Positions", "position_count": "{{count}} position", "position_count_plural": "{{count}} positions"}, "details": {"error_message": "Position data not found. Please go back and try again.", "section_title": "Position"}, "account": {"summary_title": "Account Summary", "total_balance": "Total Balance", "available_balance": "Available Balance", "margin_used": "Margin Used", "total_unrealized_pnl": "Total Unrealized P&L", "unrealized_pnl": "Unrealized P&L"}, "tpsl": {"update_success": "TP/SL updated successfully", "update_failed": "Failed to update TP/SL"}}, "markets": {"title": "Markets"}, "market": {"details": {"title": "Market Details", "error_message": "Market data not found. Please go back and try again."}, "statistics": "Overview", "24hr_high": "24hr high", "24hr_low": "24hr low", "24h_volume": "24hr volume", "open_interest": "Open interest", "funding_rate": "Funding rate", "countdown": "Countdown", "long": "<PERSON>", "short": "Short", "add_funds": "Add funds", "add_funds_to_start_trading_perps": "Add funds to start trading perps", "position": "Position", "orders": "Orders"}, "buttons": {"get_account_balance": "Get Account Balance", "deposit_funds": "Deposit Funds", "switch_to_mainnet": "Switch to Mainnet", "switch_to_testnet": "Switch to Testnet", "view_markets": "View Markets", "positions": "Positions"}, "tooltips": {"leverage": {"title": "Leverage", "content": "Leverage lets you trade with more than you put in. It can boost your profits, but also your losses. The higher the leverage, the riskier the trade."}, "liquidation_price": {"title": "Liquidation price", "content": "If the price hits this point, you'll be liquidated and lose your margin. Higher leverage makes this more likely."}, "margin": {"title": "<PERSON><PERSON>", "content": "Margin is the money you put in to open a trade. It acts as collateral, and it's the most you can lose on that trade."}, "fees": {"title": "Fees", "content": "Trading fees are charged when you open or close a position.", "metamask_fee": "MetaMask fee", "provider_fee": "Provider fee", "total": "Total fees"}, "closing_fees": {"title": "Closing Fees", "metamask_fee": "MetaMask fee", "provider_fee": "Provider fee"}, "estimated_pnl": {"title": "Estimated P&L", "content": "Your estimated profit or loss when closing this position at the current market price. This is calculated based on your entry price and includes the portion of the position you're closing."}, "limit_price": {"title": "<PERSON>it <PERSON>", "content": "The specific price at which your limit order will execute. For closing long positions, the order executes when the price rises to this level. For closing short positions, it executes when the price falls to this level."}, "open_interest": {"title": "Open interest", "content": "The combined value of all open positions for this perp."}, "funding_rate": {"title": "Funding rate", "content": "An hourly fee paid between traders to keep prices in line with the market. If the rate is positive, longs pay shorts. If negative, shorts pay longs."}, "geo_block": {"title": "Perps unavailable in your region", "content": "Perps trading isn't available in your location due to local restrictions or sanctions."}, "receive": {"title": "Receive", "content": "The amount of USDC you'll receive in your wallet after withdrawal. You'll receive USDC on the Arbitrum network."}, "withdrawal_fees": {"title": "Provider <PERSON>", "content": "A flat fee charged by the provider on each withdrawal."}, "tp_sl": {"title": "Take Profit & Stop Loss", "content": "Take Profit (TP) automatically closes your position when you reach your target profit. Stop Loss (SL) limits your losses by closing your position if the price moves against you."}, "close_position_you_receive": {"title": "Receive amount", "content": "Your receive amount is estimated and may change slightly due to slippage."}, "notifications": {"title": "Turn on notifications", "description": "Get notified about important perps trading events like order fills, liquidations, and market updates.", "turn_on_button": "Turn on"}, "got_it_button": "Got it", "tpsl_count_warning": {"title": "Multiple TP/SL orders are active", "content": "To set TP/SL for the whole position, cancel your existing TP/SL orders first.", "view_orders_button": "View orders", "got_it_button": "Got it"}}, "connection": {"failed": "Connection Failed", "error_message": "Unable to connect to Perps trading service.", "retry_connection": "Retry Connection", "retrying_connection": "Connecting...", "connecting_to_perps": "Connecting to Perps", "timeout_title": "Connection taking longer than expected"}, "chart": {"no_data": "No chart data available", "candle_intervals": "Candle intervals", "candle_period_selector": {"show_more": "More"}}, "perps_markets": "Perps markets", "volume": "Volume", "price_24h_change": "Price / 24h change", "failed_to_load_market_data": "Failed to load market data", "tap_to_retry": "Tap to retry", "search_by_token_symbol": "Search by token symbol", "testnet": "Testnet", "mainnet": "Mainnet", "developer_options": {"hyperliquid_network_toggle": "Hyperliquid Network Toggle", "simulate_connection_error": "Simulate Connection Error"}, "transactions": {"title": "Perps", "tabs": {"trades": "Trades", "orders": "Orders", "funding": "Funding", "funding_description": "Funding history is a summary of the fees you have paid or received, as determined by the funding rates on your open positions."}, "not_found": "Transaction not found", "position": {"date": "Date", "size": "Size", "entry_price": "Entry price", "close_price": "Close price", "points": "Points", "fees": "Total fees", "pnl": "Net P&L"}, "order": {"date": "Date", "size": "Size", "limit_price": "Limit price", "filled": "Filled", "metamask_fee": "MetaMask fee", "hyperliquid_fee": "Hyperliquid fee", "total_fee": "Total fee"}, "funding": {"date": "Date", "fee": "Fee", "rate": "Rate"}, "view_on_explorer": "View on block explorer", "empty_state": {"no_transactions": "No {{type}} transactions yet", "history_will_appear": "Your trading history will appear here"}}, "risk_disclaimer": "Perps trading is risky, and you could suddenly and without notice lose your entire margin. You trade entirely at your own risk. Market data provided by Hyperliquid. Price chart powered by", "tutorial": {"continue": "Continue", "skip": "<PERSON><PERSON>", "add_funds": "Add funds", "what_are_perps": {"title": "What are perps?", "description": "MetaMask now supports perpetual futures—aka perps—letting you trade on a token's price movement without buying it.", "subtitle": "Here's how it works."}, "go_long_or_short": {"title": "Go long or short on a token", "description": "Pick a token to long or short, then set your order size.", "subtitle": "Go long to profit if the price goes up. Go short to profit if the price goes down."}, "choose_leverage": {"title": "Choose your leverage", "description": "Leverage amplifies both gains and losses. With 10x leverage, a 1% price move = 10% gain or loss on your margin."}, "watch_liquidation": {"title": "Watch out for liquidation", "description": "You'll lose your entire margin if the token hits your liquidation price. Higher leverage means less room to liquidation."}, "close_anytime": {"title": "Close any time", "description": "Exit whenever you want. You'll get back your margin, plus profits or minus losses."}, "ready_to_trade": {"title": "Ready to trade?", "fund_text_helper": "MetaMask will swap your funds to USDC on Arbitrum, and deposit them onto HyperEVM for no added fee.", "description": "Fund your perps account with any token and make your first trade in seconds."}, "got_it": "Got it"}}, "receive": {"title": "Receive"}, "experience_enhancer_modal": {"title": "Help us enhance your experience", "paragraph1a": "In addition to ", "paragraph1b": ", we'd like to use data (like information from cookies) to learn how you interact with our marketing communications.", "link": "MetaMetrics", "paragraph2": "This helps us personalize what we share with you, like:", "bullet1": "Latest developments", "bullet2": "Product features", "bullet3": "Other relevant promotional materials", "footer": "Remember, we never sell the data you provide and you can opt out any time.", "accept": "I agree", "cancel": "No thanks"}, "multi_rpc_migration_modal": {"description": "We now support multiple RPCs for a single network. Your most recent RPC has been selected as the default one to resolve conflicting information", "accept": "Accept"}, "qr_tab_switcher": {"scanner_tab": "Scan QR code", "receive_tab": "Your QR code"}, "banner": {"bridge": {"title": "Ready to bridge?", "subtitle": "Move across 9 chains, all within your wallet"}, "card": {"title": "MetaMask Card", "subtitle": "Available in select regions"}, "fund": {"title": "Fund your wallet", "subtitle": "Add or transfer tokens to get started"}, "cashout": {"title": "Cash out with MetaMask", "subtitle": "Sell your crypto for cash"}, "aggregated": {"title": "Your balance is aggregated", "subtitle": "Control your balance view in settings"}, "multisrp": {"title": "Add multiple Secret Recovery Phrases", "subtitle": "Import and use multiple wallets in MetaMask"}, "solana": {"title": "Solana is now supported", "subtitle": "Create a Solana account to get started"}, "smartAccount": {"title": "Start using smart accounts", "subtitle": "Same address, smarter features"}, "backupAndSync": {"title": "Introducing backup and sync", "subtitle": "Back up your accounts and sync settings."}}, "wallet": {"title": "Wallet", "tokens": "Tokens", "collectible": "Collectible", "collectibles": "NFTs", "defi": "<PERSON><PERSON><PERSON>", "perps": "Perps", "transactions": "TRANSACTIONS", "no_collectibles": "Don’t see your NFT?", "no_available_tokens": "Don't see your token?", "add_tokens": "Import tokens", "are_you_sure_exit": "Are you sure you want to exit?", "search_information_not_saved": "Your search information will not be saved.", "import_token": "Would you like to import this token?", "tokens_detected_in_account": "{{tokenCount}} new {{tokensLabel}} found in this account", "token_toast": {"tokens_imported_title": "Imported Tokens", "tokens_imported_desc": "Successfully imported {{tokenSymbols}}", "token_imported_title": "Imported Token", "token_imported_desc": "Successfully imported {{tokenSymbol}}", "token_imported_desc_1": "You’ve imported a token.", "tokens_import_success_multiple": "You’ve imported {{tokensNumber}} tokens.", "tokens_hidden_title": "Hiding <PERSON>s", "tokens_hidden_desc": "Hiding detected tokens from your wallet", "token_hidden_title": "Hiding <PERSON>", "token_hidden_desc": "Hiding {{tokenSymbol}}"}, "hide_token": {"title": "Hide Token?", "desc": "You can add this token back in the future by going to “Import token” and searching for the token.", "cancel_cta": "Cancel", "confirm_cta": "Confirm"}, "import": "Import", "sort_by": "Sort by", "filter_by": "Filter by", "networks": "Networks", "popular": "Popular", "custom": "Custom", "current_network": "Current network", "popular_networks": "Popular networks", "all_networks": "All Networks", "declining_balance": "Declining balance ({{currency}} high-low)", "alphabetically": "Alphabetically (A-Z)", "add_to_get_started": "Add crypto to get started", "token_is_needed_to_continue": "{{tokenSymbol}} is needed to continue", "fund_your_wallet_to_get_started": "Fund your wallet to get started in web3", "add_funds": "Add funds", "next": "Next", "buy_asset": "Buy {{asset}}", "no_tokens": "You don't have any tokens!", "show_tokens_without_balance": "Show tokens without balance", "no_nfts_yet": "No NFTs yet", "nfts_autodetection_title": "NFT detection", "nfts_autodetection_desc": "Let MetaMask automatically detect and display NFTs in your wallet.", "network_details_check": "Network details check", "network_with_chain_id": "The network with chain ID", "chain_list_returned_different_ticker_symbol": "This token symbol doesn't match the network name or chain ID entered. Many popular tokens use similar symbols, which scammers can use to trick you into sending them a more valuable token in return. Verify everything before you continue.", "suggested_token_symbol": "Suggested ticker symbol:", "potential_scam": "This is a potential scam", "network_not_matching": "This network doesn't match its associated chain ID or name. Many popular tokens use the name", "target_scam_network": "making it a target for scams. Scammers may trick you into sending them more valuable currency in return. Verify everything before you continue.", "use_the_currency_symbol": "uses the currency symbol", "use_correct_symbol": "Make sure you’re using the correct symbol before continuing", "chain_id_currently_used": "This Chain ID is currently used by the", "incorrect_network_name_warning": "According to our records, the network name may not correctly match this chain ID.", "suggested_name": "Suggested name:", "network_check_validation_desc": "reduces your chances of connecting to a malicious or incorrect network.", "cant_verify_custom_network_warning": "We can’t verify custom networks. To avoid malicious providers from recording your network activity, only add networks you trust.", "nfts_autodetection_cta": "Turn on NFT detection in Settings", "learn_more": "Learn more", "add_collectibles": "Import NFTs", "no_transactions": "You have no transactions!", "switch_network_to_view_transactions": "Please switch network to view transactions", "send_button": "Send", "deposit_button": "<PERSON><PERSON><PERSON><PERSON>", "copy_address": "Copy", "collectible_action_title": "Collectible Options", "remove_token_title": "Do you want to hide this token?", "remove_collectible_title": "Do you want to remove this collectible?", "refresh_metadata": "<PERSON>f<PERSON>", "token_removal_issue_title": "Issue removing token.", "token_removal_issue_desc": "There was a problem when trying to remove the token. Try again.", "collectible_removed_title": "Collectible removed!", "collectible_removed_desc": "If you change your mind, you can add it back by tapping on \"Import NFTs\"", "remove": "<PERSON>de", "cancel": "Cancel", "yes": "Yes", "private_key_detected": "Private key detected", "do_you_want_to_import_this_account": "Do you want to import this account?", "error": "Error", "logout_to_import_seed": "You need to log out first in order to import a Secret Recovery Phrase.", "ready_to_explore": "Ready to start exploring blockchain applications?", "unable_to_load": "Unable to load balance", "unable_to_find_conversion_rate": "no conversion rate", "display_nft_media_desc": "To import an NFT, turn on Display NFT media in Settings > Security and privacy.", "display_nft_media_cta": "Turn on Display NFT media", "display_media_nft_warning": "Displaying NFT media and data may expose your IP address to centralized servers. Only import an NFT if you understand the risks involved.", "nfts_autodetect_title": "NFT autodetection", "nfts_autodetect_cta": "Enable NFT autodetection", "turn_on_network_check_cta": "Turn on network details check", "display_nft_media_cta_new_1": "To see an NFT, turn on Display NFT media in", "display_nft_media_cta_new_2": "Settings > Security and Privacy.", "banner": {"title": "Basic functionality is off", "link": "Turn on basic functionality"}, "carousel": {"empty_state": "You're all caught up!"}}, "asset_details": {"token": "Token", "amount": "<PERSON><PERSON> Amount", "address": "Token contract address", "decimal": "Token decimal", "network": "Network", "network_fee": "Network fee", "lists": "Token Lists", "hide_cta": "Hide token", "options": {"view_on_portfolio": "View on Portfolio", "view_on_block": "View on block explorer", "token_details": "Token details", "remove_token": "Remove token"}}, "nft_details": {"bought_for": "Bought for", "highest_floor_price": "Highest floor price", "data_unavailable": "data unavailable", "price_unavailable": "price unavailable", "rank": "Rank", "contract_address": "Contract address", "token_id": "Token ID", "token_symbol": "Token symbol", "token_standard": "Token standard", "date_created": "Date created", "unique_token_holders": "Unique token holders", "tokens_in_collection": "Tokens in collection", "creator_address": "Creator address", "last_sold": "Last sold", "highest_current_bid": "Highest current bid", "options": {"view_on_os": "View on OpenSea", "remove_nft": "Remove NFT"}, "attributes": "Attributes", "disclaimer": "Disclaimer: MetaMask pulls the media file from the source URL. This URL is sometimes changed by the marketplace the NFT was minted on."}, "activity_view": {"title": "Activity"}, "transactions_view": {"title": "Transactions"}, "add_asset": {"title": "Import tokens", "title_nft": "Import NFT", "search_token": "Search", "custom_token": "Custom token", "tokens": {"cancel_add_token": "CANCEL", "add_token": "IMPORT"}, "collectibles": {"cancel_add_collectible": "CANCEL", "add_collectible": "IMPORT"}, "banners": {"search_desc": "Improved token detection is currently available on {{network}} network. ", "search_link": "Enable it from Settings.", "custom_warning_desc": "Anyone can create a token, including creating fake versions of existing tokens. Learn more about ", "custom_warning_link": "scams and security risks.", "custom_info_desc": "Token detection is not available on this network yet. Please import token manually and make sure you trust it. Learn about ", "custom_info_link": "token scams and security risks.", "custom_security_tips": "Security Tips"}}, "defi_positions": {"loading_positions": "Loading DeFi positions...", "no_visible_positions": "Can't find what you're looking for?", "not_supported": "We may not support your protocol yet.", "error_cannot_load_page": "We could not load this page.", "error_visit_again": "Try visiting again later.", "single_token": "{{symbol}} only", "two_tokens": "{{symbol}} +1 other", "multiple_tokens": "{{symbol}} +{{count}} others", "supply": "Supplied", "stake": "Staked", "borrow": "Borrowed", "reward": "Rewards"}, "terms_and_conditions": {"title": "Terms and Conditions", "description": "By proceeding, you agree to these ", "terms": "Terms and Conditions"}, "privacy_policy": {"title": "Privacy Policy", "fine_print_1": "We’ll let you know if we decide to use this data for other purposes. You can review our", "fine_print_2": "for more information. Remember, you can go to settings and opt out at any time.", "privacy_policy_button": "Privacy Policy", "agree": "I Agree", "decline": "No Thanks", "description_title": "Help us improve MetaMask", "description_content_1": "We’d like to gather basic usage data to improve MetaMask. Know that we never sell the data you provide here.", "description_content_2": "When we gather metrics, it will always be...", "description_content_3": "Learn how we protect your privacy while collecting usage data for your profile.", "checkbox": "We'll use this data to learn how you interact with our marketing communications. We may share relevant news (like product features).", "action_description_1_prefix": "Private:", "action_description_2_prefix": "General:", "action_description_3_prefix": "Optional:", "action_description_1_description": "Clicks and views on the app are stored, but other details (like your public address) are not.", "action_description_2_description": "We temporarily use your IP address to detect a general location (like your country or region), but it's never stored.", "action_description_3_description": "You decide if you want to share or delete your usage data via settings any time.", "cta_no_thanks": "No thanks", "cta_i_agree": "I agree", "fine_print_1_legacy": "This data is aggregated and is therefore anonymous for the purposes of General Data Protection Regulation (EU) 2016/679.", "fine_print_2a_legacy": "*When you use Infura as your default RPC provider in MetaMask, Infura will collect your IP address and your Ethereum wallet address when you send a transaction. We don’t store this information in a way that allows our systems to associate those two pieces of data. You can configure your RPC provider", "fine_print_2b_legacy": "before proceeding.\nFor more information on how MetaMask and Infura interact from a data collection perspective, see our update", "fine_print_2c_legacy": ". For more information on our privacy practices in general, see our Privacy Policy", "here_legacy": "here", "description_content_1_legacy": "MetaMask would like to gather usage data to better understand how our users interact with MetaMask. This data will be used to provide the service, which includes improving the service based on your use.", "description_content_2_legacy": "MetaMask will...", "action_description_1_legacy": "Always allow you to opt-out via Settings", "action_description_2_legacy": "Send anonymized click & pageview events", "action_description_3_legacy": "collect information we don’t need to provide the service (such as keys, addresses, transaction hashes, or balances)", "action_description_4_legacy": "collect your full IP address*", "action_description_5_legacy": "sell data. Ever!", "action_description_never_legacy": "Never", "toast_message": "We’ve updated our privacy policy", "toast_action_button": "Close", "toast_read_more": "Read more"}, "template_confirmation": {"ok": "OK", "cancel": "Cancel"}, "approval_result": {"ok": "OK", "success": "Success", "error": "Error", "resultPageSuccessDefaultMessage": "The operation completed successfully.", "resultPageErrorDefaultMessage": "The operation failed."}, "token": {"token_symbol": "Token symbol", "token_address": "Token Address", "token_decimal": "Token decimal", "search_tokens_placeholder": "Search Tokens", "address_cant_be_empty": "Token address can't be empty.", "address_must_be_valid": "Token address must be a valid address.", "symbol_cant_be_empty": "Token symbol can't be empty.", "symbol_length": "Symbol must be 11 characters or fewer", "decimals_cant_be_empty": "Token decimals can't be empty.", "decimals_is_required": "Decimal is required. Find it on:", "no_tokens_found": "We couldn't find any tokens with that name.", "select_token": "Select Token", "address_must_be_smart_contract": "Personal address detected. Enter the token contract address.", "billion_abbreviation": "B", "trillion_abbreviation": "T", "million_abbreviation": "M", "token_details": "Token details", "contract_address": "Contract address", "token_list": "Token list", "market_details": "Market details", "market_cap": "Market Cap", "total_volume": "Total Volume (24h)", "volume_to_marketcap": "Volume / Market Cap", "circulating_supply": "Circulating supply", "all_time_high": "All time high", "all_time_low": "All time low", "fully_diluted": "Fully diluted", "unknown": "Unknown"}, "collectible": {"collectible_address": "Address", "collectible_type": "Type", "collectible_token_id": "ID", "collectible_description": "Description", "address_must_be_valid": "Collectible address must be a valid address.", "address_must_be_smart_contract": "Personal address detected. Input the collectible contract address.", "address_cant_be_empty": "Collectible address can't be empty.", "token_id_cant_be_empty": "Collectible identifier can't be empty.", "not_owner_error_title": "Something happened.", "not_owner_error": "You are not the owner of this collectible, so you can't add it.", "ownership_verification_error_title": "Unable to add NFT", "ownership_verification_error": "We could not verify ownership. This may be because the standard is not supported or the asset does not exist on your selected network.", "powered_by_opensea": "Powered by", "id_placeholder": "Enter the collectible ID", "collectible_token_standard": "Token Standard", "collectible_last_sold": "Last sold", "collectible_last_price_sold": "Last price sold", "collectible_source": "Source", "collectible_link": "Link", "collectible_asset_contract": "Asset contract", "share_check_out_nft": "Check out my NFT!", "share_via": "Shared via", "untitled_collection": "Untitled Collection", "collection": "Collection"}, "transfer": {"title": "Transfer", "send": "SEND", "receive": "RECEIVE"}, "accounts": {"srp_index": "SRP #{{index}}", "snap_account_tag": "Snaps (Beta)", "create_new_account": "Create a new account", "new_account": "New account", "import_account": "Import an account", "connect_hardware": "Connect hardware wallet", "imported": "Imported", "qr_hardware": "QR hardware", "remove_account_title": "Account removal", "remove_account_message": "Do you really want to remove this account?", "no": "No", "yes_remove_it": "Yes, remove it", "remove_hardware_account": "Remove hardware account", "remove_hw_account_alert_description": "Are you sure you want to remove this hardware wallet account? You’ll have to resync your hardware wallet if you want to use this account again with MetaMask Mobile.", "remove_snap_account": "Remove snap account", "remove_snap_account_alert_description": "This account will be removed from your wallet. Please make sure you have the original Secret Recovery Phrase or private key for this imported account before continuing. You can import or create accounts again from the account drop-down.", "remove_account_alert_remove_btn": "Remove", "remove_account_alert_cancel_btn": "Nevermind", "accounts_title": "Accounts", "connect_account_title": "Connect account", "connect_accounts_title": "Connect accounts", "edit_accounts_title": "Edit accounts", "connected_accounts_title": "Connected accounts", "connect_description": "Share your account address, balance, activity, and allow site to initiate transactions.", "select_accounts_description": "Select the account(s) to use on this site:", "connect_multiple_accounts": "Connect multiple accounts", "connect_more_accounts": "Connect more accounts", "add": "Add", "cancel": "Cancel", "connect": "Connect", "connect_with_count": "Connect{{<PERSON><PERSON><PERSON><PERSON>}}", "select_all": "Select all", "unselect_all": "Unselect all", "permissions": "Permissions", "revoke": "Revoke", "revoke_all": "Revoke all", "ledger": "Ledger", "site_permission_to": "This site has permission to:", "address_balance_activity_permission": "See address, account balance, and activity", "suggest_transactions": "Suggest transactions to approve", "accounts_connected": "accounts connected", "account_connected": "account connected", "accounts_disconnected": "accounts disconnected.", "account_disconnected": "account disconnected.", "disconnect": "Disconnect", "disconnect_all": "Disconnect all", "reconnect_notice": "If you disconnect from {{dappUrl}}, you’ll need to reconnect your accounts and networks to use this site again.", "disconnect_all_accounts": "Disconnect all accounts", "deceptive_site_ahead": "Deceptive site ahead", "deceptive_site_desc": "The site you’re trying to visit isn’t safe. Attackers may trick you into doing something dangerous.", "learn_more": "Learn more", "advisory_by": "Advisory provided by Ethereum Phishing Detector and PhishFort", "potential_threat": "Potential threats include", "fake_metamask": "Fake versions of MetaMask", "srp_theft": "Secret recovery phrase or password theft", "malicious_transactions": "Malicious transactions resulting in stolen assets", "secret_recovery_phrase": "Secret Recovery Phrase", "account_name": "Account name", "select_secret_recovery_phrase": "Select Secret Recovery Phrase", "reveal_secret_recovery_phrase": "Reveal Secret Recovery Phrase", "add_new_hd_account_helper_text": "The Secret Recovery Phrase your new account will be generated from", "accounts": "accounts", "show_accounts": "Show", "hide_accounts": "<PERSON>de", "labels": {"bitcoin_testnet_account_name": "Bitcoin Testnet Account", "bitcoin_account_name": "Bitcoin Account", "bitcoin_signet_account_name": "Bitcoin Signet Account", "bitcoin_regtest_account_name": "Bitcoin Regtest Account", "solana_devnet_account_name": "<PERSON><PERSON> Account", "solana_testnet_account_name": "<PERSON><PERSON>net Account", "solana_account_name": "<PERSON><PERSON> Account"}, "error_messages": {"failed_to_create_account": "Failed to create {{clientType}} account"}, "account_connect_create_initial_account": {"description": "A Solana account is required to connect to this site.", "button": "Create Solana account"}, "no_accounts_found": "No accounts found", "no_accounts_found_for_search": "No accounts found matching your search", "search_your_accounts": "Search your accounts"}, "toast": {"connected_and_active": "connected and active.", "now_active": "now active.", "network_added": "was successfully added", "network_removed": "was successfully removed", "network_deleted": "was successfully deleted", "network_permissions_updated": "Network permissions updated", "revoked": "revoked.", "revoked_all": "All accounts revoked.", "accounts_connected": "accounts connected.", "account_connected": "account connected.", "accounts_permissions_updated": "Accounts permissions updated", "accounts_disconnected": "accounts disconnected.", "account_disconnected": "account disconnected.", "disconnected": "disconnected.", "disconnected_all": "All accounts disconnected.", "disconnected_from": "Disconnected from {{dappHostName}}", "permissions_updated": "Permissions updated", "nft_detection_enabled": "NFT autodetection enabled"}, "connect_qr_hardware": {"title": "Connect a QR-based hardware wallet", "description1": "Connect an airgapped hardware wallet that communicates through QR-codes.", "description2": "How it works?", "description3": "Officially supported airgapped hardware wallets include:", "keystone": "Keystone", "ngravezero": "<PERSON><PERSON>", "learnMore": "Learn more", "buyNow": "Buy now", "tutorial": "Tutorial", "description4": "<PERSON><PERSON> (tutorial)", "description5": "1. Unlock your Keystone", "description6": "2. Tap the ··· Menu, then go to Sync", "button_continue": "Continue", "hint_text": "Scan your hardware wallet to ", "purpose_connect": "connect", "purpose_sign": "confirm the transaction", "select_accounts": "Select an Account"}, "data_collection_modal": {"accept": "Okay", "content": "You turned off data collection for our marketing purposes. This only applies to this device. If you use MetaMask on other devices, make sure to opt out there as well."}, "account_selector": {"prev": "PREV", "next": "NEXT", "unlock": "Unlock", "forget": "Forget this device"}, "address_selector": {"select_an_address": "Select an address"}, "app_settings": {"enabling_notifications": "Enabling notifications...", "updating_notifications": "Updating notifications...", "updating_account_settings": "Updating account settings...", "reset_notifications_title": "Reset notifications", "reset_notifications_description": "Resetting notifications, means you're deleting your notifications storage keys and resetting all your notification history. Are you sure you want to do this?", "reset_notifications": "Reset notifications", "reset_notifications_success": "Notifications storage key deleted/recreated, and notifications history reset.", "notifications_dismiss_modal": "<PERSON><PERSON><PERSON>", "select_rpc_url": "Select RPC URL", "title": "Settings", "current_conversion": "Base Currency", "current_language": "Current Language", "ipfs_gateway": "IPFS Gateway", "ipfs_gateway_content": "MetaMask uses third-party services to show images of your NFTs stored on IPFS, display information related to ENS addresses entered in your browser's address bar, and fetch icons for different tokens. Your IP address may be exposed to these services when you’re using them.", "ipfs_gateway_down": "Your current IPFS gateway is down", "ipfs_gateway_desc": "Choose your preferred IPFS gateway.", "search_engine": "Search Engine", "new_RPC_URL": "New RPC Network", "state_logs": "State Logs", "add_network_title": "Add a network", "auto_lock": "Auto-lock", "auto_lock_desc": "Choose the amount of time before the application automatically locks.", "state_logs_desc": "This will help MetaMask debug any issue you might encounter. Please send it to MetaMask support via hamburger icon > Send Feedback, or reply to your existing ticket if you have one.", "autolock_immediately": "Immediately", "autolock_never": "Never", "autolock_after": "After {{time}} seconds", "autolock_after_minutes": "After {{time}} minutes", "reveal_seed_words": "Reveal Seed Words", "reset_account": "Reset Account", "state_logs_button": "Download State Logs", "reveal_seed_words_button": "REVEAL SEED WORDS", "reset_account_button": "Reset Account", "reset_account_confirm_button": "Yes, reset", "reset_account_cancel_button": "Cancel", "reset_account_modal_title": "Reset Account?", "clear_approvals_modal_title": "Clear Approval Data?", "clear_approvals_modal_message": "All dapps will need to request access to view account information again.", "clear_browser_history_modal_title": "Clear Browser History?", "clear_browser_history_modal_message": "We are about to remove all your browser history. Are you sure?", "clear_cookies_modal_title": "Clear Browser Cookies", "clear_cookies_modal_message": "We are about to remove your browser's cookies. Are you sure?", "reset_account_modal_message": "Resetting your account will clear your transaction activity.", "save_rpc_url": "SAVE", "invalid_rpc_prefix": "URIs require the appropriate HTTPS prefix", "invalid_rpc_url": "Invalid RPC URL", "invalid_block_explorer_url": "Invalid Block Explorer URL", "sync": "SYNC", "clear_approved_dapps": "CLEAR APPROVED DAPPS", "clear_browser_history": "CLEAR BROWSER HISTORY", "clear_approve_dapps_desc": "Clear approved dapps", "clear_browser_history_desc": "Clear browser history", "clear_browser_cookies_desc": "Clear browser cookies", "clear": "CLEAR", "protect_cta": "Protect", "protect_title": "Wallet recovery", "banner_social_login_enabled": "Log in with {{authConnection}}", "manage_recovery_method": "Manage recovery methods", "video_failed": "Video Failed to Load.", "protect_desc": "Back up your Secret Recovery Phrase so you never lose access to your wallet. Be sure to store it in a safe place that only you can access and won’t forget", "protect_desc_no_backup": "This is your wallet’s 12 word phrase. This phrase can be used to take control of all your current and future accounts, including the ability to send away funds in your wallet. Keep this phrase stored safely, DO NOT share it with anyone. MetaMask cannot help you recover this key.", "learn_more": "Learn more.", "seedphrase_not_backed_up": "Important! Secret Recovery Phrase not backed up", "seedphrase_backed_up": "Secret Recovery Phrase backed up", "back_up_now": "Back up now", "back_up_again": "Back up again", "view_hint": "View hint", "privacy_mode": "Privacy mode", "privacy_mode_desc": "Websites must request access to view your account information.", "nft_opensea_mode": "Enable OpenSea API", "nft_opensea_desc": "Displaying NFT media & data may expose your IP address to centralized servers. Use OpenSea's API to fetch NFT data. NFT auto-detection relies on OpenSea's API, and will not be available when this is turned off. Enabling NFT auto-detection can expose you to fake NFTs being sent to your wallet by anyone, and can allow an attacker to learn your IP address from your Ethereum address.", "nft_autodetect_mode": "Autodetect NFTs", "nft_autodetect_desc": "Displaying NFT media & data may expose your IP address to centralized servers. Third-party APIs (like OpenSea) are used to detect NFTs in your wallet. This exposes your account address with those services. Leave this disabled if you don't want the app to pull data from those services.", "show_fiat_on_testnets": "Show conversion on test networks", "show_fiat_on_testnets_desc": "Select this to show fiat conversion on test networks", "show_fiat_on_testnets_modal_title": "Be careful", "show_fiat_on_testnets_modal_description": "If you've been asked to turn this feature on, you might be getting scammed. These tokens have no monetary value and are for testing purposes only. This feature helps developers make sure their apps work.", "show_fiat_on_testnets_modal_learn_more": "Learn more.", "show_fiat_on_testnets_modal_button": "Continue", "show_hex_data": "Show Hex Data", "show_hex_data_desc": "Select this to show the hex data field on the send screen.", "show_custom_nonce": "Customize transaction nonce", "custom_nonce_desc": "Turn this on to change the nonce (transaction number) on confirmation screens. This is an advanced feature, use cautiously.", "accounts_identicon_title": "Account icon", "accounts_identicon_desc": "Choose from three different styles of unique icons that can help you identify accounts at a glance.", "jazzicons": "Jazzicons", "blockies": "Blockies", "general_title": "General", "general_desc": "Currency conversion, primary currency, language and search engine", "advanced_title": "Advanced", "advanced_desc": "Access developer features, reset account, setup testnets, state logs, IPFS gateway and custom RPC", "notifications_title": "Notifications", "notifications_desc": "Manage your notifications", "allow_notifications": "Allow notifications", "enable_push_notifications": "Enable push notifications", "allow_notifications_desc": "Stay in the loop on what’s happening in your wallet with notifications. To use notifications, we use a profile to sync some settings across your devices.", "notifications_opts": {"customize_session_title": "Customize your notifications", "customize_session_desc": "Turn on the types of notifications you want to receive:", "account_session_title": "Account activity", "account_session_desc": "Select the accounts you want to be notified about:", "assets_sent_title": "<PERSON><PERSON>", "assets_sent_desc": "Funds and NFT", "assets_received_title": "Assets Received", "assets_received_desc": "Funds and NFT", "defi_title": "<PERSON><PERSON><PERSON>", "defi_desc": "Staking, swapping, and bridging", "snaps_title": "Snaps", "snaps_desc": "New features and updates", "products_announcements_title": "Product announcements", "products_announcements_desc": "New products and features", "perps_title": "Perps trading"}, "contacts_title": "Contacts", "contacts_desc": "Add, edit, remove, and manage your accounts", "permissions_title": "Permissions", "permissions_desc": "Manage the permissions given to sites and apps", "no_permissions": "No permissions", "no_permissions_desc": "If you connect an account to a site or an app, you’ll see it here.", "security_title": "Security & Privacy", "back": "Back", "security_desc": "Privacy settings, MetaMetrics, private key, and Secret Recovery Phrase.", "networks_title": "Networks", "networks_default_title": "Default Network", "network_delete": "If you delete this network, you will need to add it again to view your assets in this network", "networks_default_cta": "Use this network", "add_rpc_url": "Add RPC URL", "add_block_explorer_url": "Add Block Explorer URL", "networks_desc": "Add and edit custom RPC networks", "network_name_label": "Network Name", "network_name_placeholder": "Network Name (optional)", "network_rpc_url_label": "RPC URL", "network_rpc_name_label": "RPC Name", "network_rpc_placeholder": "New RPC Network", "network_failover_rpc_url_label": "Failover RPC URL", "failover": "Failover", "network_chain_id_label": "Chain ID", "network_chain_id_placeholder": "Chain ID", "network_symbol_label": "Symbol", "network_block_explorer_label": "Block Explorer URL", "network_block_explorer_placeholder": "Block Explorer URL (optional)", "network_chain_id_warning": "Invalid Chain ID", "network_other_networks": "Other Networks", "network_rpc_networks": "RPC Networks", "network_add_network": "Add Network", "network_add_custom_network": "Add a custom network", "network_add": "Add", "network_save": "Save", "remove_network_title": "Do you want to remove this network?", "remove_network": "Remove", "cancel_remove_network": "Cancel", "info_title": "About MetaMask", "info_title_beta": "About MetaMask Beta", "info_title_flask": "About MetaMask Flask", "experimental_title": "Experimental", "experimental_desc": "WalletConnect & more...", "legal_title": "Legal", "conversion_title": "Currency conversion", "conversion_desc": "Display fiat values in using a specific currency throughout the application.", "primary_currency_title": "Primary Currency", "primary_currency_desc": "Select Native to prioritize displaying values in the native currency of the chain (e.g. ETH). Select Fiat to prioritize displaying values in your selected fiat currency.", "primary_currency_text_first": "Native", "primary_currency_text_second": "Fiat", "language_desc": "Translate the application to a different supported language.", "engine_desc": "Change the default search engine used when entering search terms in the URL bar.", "reset_desc": "This action will clear your transaction activity. This data might not be retrievable.", "rpc_desc": "Use a custom RPC-capable network via URL instead of one of the provided networks.", "hex_desc": "Select this to show the hex data field on the send screen.", "clear_privacy_title": "Clear privacy data", "clear_privacy_desc": "Clear privacy data so all websites must request access to view account information again.", "clear_history_desc": "Choose this option to clear your entire browsing history.", "clear_cookies_desc": "Choose this option to clear your browser's cookies.", "metametrics_title": "Participate in MetaMetrics", "metametrics_description": "Allow MetaMetrics to collect basic usage and diagnostics data to improve our product. You can disable MetaMetrics for this device.", "data_collection_title": "Data collection for marketing", "data_collection_description": "We’ll use MetaMetrics to learn how you interact with our marketing communications. We may share relevant news (like product features and other materials).", "batch_balance_requests_title": "Batch account balance requests", "batch_balance_requests_description": "Get balance updates for all your accounts at once. Turning off this feature means others are less likely to associate one account with another.", "third_party_title": "Get incoming transactions", "third_party_description": "Third party APIs (Etherscan) are used to show your incoming transactions in the history. Turn off if you don’t want us to pull data from those services.", "metametrics_opt_out": "MetaMetrics Opt-out", "metametrics_restart_required": "You need to restart the app for the changes to take effect.", "create_password": "Create Password", "invalid_password": "Invalid password", "invalid_password_message": "The password was not correct. Please try again.", "security_heading": "Security", "general_heading": "General", "privacy_heading": "Privacy", "failed_to_fetch_chain_id": "Could not fetch chain ID. Is your RPC URL correct?", "endpoint_returned_different_chain_id": "The endpoint returned a different chain ID: %{chainIdReturned}", "chain_id_required": "The chain ID is required. It must match the chain ID returned by the network. You can enter a decimal or '0x'-prefixed hexadecimal number.", "invalid_hex_number": "Invalid hexadecimal number.", "invalid_hex_number_leading_zeros": "Invalid hexadecimal number. Remove any leading zeros.", "invalid_number": "Invalid number. Enter a decimal or '0x'-prefixed hexadecimal number.", "invalid_number_leading_zeros": "Invalid number. Remove any leading zeros.", "invalid_number_range": "Invalid number. Enter a number between 1 and %{maxSafeChainId}", "hide_zero_balance_tokens_title": "Hide Tokens Without Balance", "hide_zero_balance_tokens_desc": "Prevents tokens with no balance from displaying in your token listing.", "token_detection_title": "Autodetect tokens", "token_detection_description": "We use third-party APIs to detect and display new tokens sent to your wallet. Turn off if you don’t want the app to pull data from those services.", "theme_button_text": "Change Theme", "theme_title": "Theme ({{theme}})", "theme_description": "Change your app appearance by setting the theme.", "theme_os": "System", "theme_light": "Light", "theme_dark": "Dark", "mainnet": "Mainnet", "test_network_name": "Test networks", "custom_network_name": "Custom networks", "popular": "Popular", "delete": "Delete", "account": "account", "accounts": "accounts", "network": "network", "networks": "networks", "network_exists": "This network has already been added.", "unMatched_chain": "According to our records, this URL does not match a known provider for this chain ID.", "unMatched_chain_name": "This chain ID doesn’t match the network name.", "url_associated_to_another_chain_id": "This URL is associated with another chain ID.", "chain_id_associated_with_another_network": "The information you have entered is associated with an existing chain ID. Update your information or", "network_already_exist": "You already have a network with the same chain ID or RPC URL. Enter a new chain ID or RPC URL", "edit_original_network": "edit the original network", "find_the_right_one": "Find the right one on:", "delete_metrics_title": "Delete MetaMetrics data", "delete_metrics_description_part_one": "This will delete historical", "delete_metrics_description_part_two": "MetaMetrics", "delete_metrics_description_part_three": "data associated with your wallet.", "delete_metrics_description_before_delete": "Your wallet and accounts will remain exactly as they are now after this data has been deleted. This process may take up to 30 days. View our", "delete_metrics_description_after_delete_part_one": "You initiated this action on", "delete_metrics_description_after_delete_part_two": ". This process can take up to 30 days. View our", "delete_metrics_description_privacy_policy": "Privacy Policy.", "delete_metrics_button": "Delete MetaMetrics data", "check_status_button": "Check Status", "delete_metrics_confirm_modal_title": "Delete MetaMetrics data?", "delete_metrics_confirm_modal_description": "We are about to remove all your MetaMetrics data. Are you sure?", "delete_wallet_data_title": "Reset wallet", "delete_wallet_data_description": "This will remove all wallet related data from your device. Your accounts exist on the blockchain and are not related to MetaMask. You can always recover your accounts using your Secret Recovery Phrase.", "delete_wallet_data_button": "Reset wallet", "delete_data_status_title": "Deletion Task Status", "delete_data_status_description": "The current status is", "delete_metrics_error_title": "We are unable to delete this data right now.", "delete_metrics_error_description": "This request can't be completed right now due to an analytics system server issue, please try again later.", "ok": "OK", "clear_sdk_connections_title": "Clear all MetaMask SDK Connections", "clear_sdk_connections_text": "All connections will be cleared and da<PERSON> will need to request connection again", "sdk_connections": "MetaMask SDK Connections", "manage_sdk_connections_title": "Manage connections", "manage_sdk_connections_text": "Remove connections to sites and/or MetaMask SDK.", "fiat_on_ramp": {"title": "Buy & Sell Crypto", "description": "Region & more...", "current_region": "Current Region", "reset_region": "Reset Region", "no_region_selected": "No region selected", "sdk_activation_keys": "SDK Activation Keys", "activation_keys_description": "Activation Keys will enable specific features or providers.", "add_activation_key": "Add Activation Key", "edit_activation_key": "Edit Activation Key", "paste_or_type_activation_key": "Paste or type an Activation Key", "add_label": "Add a label to this key", "label": "Label", "key": "Key", "add": "Add", "update": "Update", "cancel": "Cancel", "deposit_provider_logout_button": "Log out of {{depositProviderName}}", "deposit_provider_logged_out": "Logged out of {{depositProviderName}}"}, "request_feature": "Request a feature", "contact_support": "Contact support", "display_nft_media": "Display NFT Media", "display_nft_media_desc": "Displaying NFT media and data exposes your IP address to OpenSea or other third parties. NFT autodetection relies on this feature, and won't be available when this is turned off.", "autodetect_nft_desc": "Let MetaMask add NFTs you own using third-party services (like OpenSea). Autodetecting NFTs exposes your IP and account address to these services. Enabling this feature could associate your IP address with your Ethereum address and display fake NFTs airdropped by scammers. You can add tokens manually to avoid this risk.", "display_nft_media_desc_new": "Displaying NFT media and data exposes your IP address to OpenSea or other third parties. NFT autodetection relies on this feature, and won't be available when turned off. If NFT media is fully located on IPFS, it can still be displayed even when this feature is turned off.", "use_safe_chains_list_validation_desc_1": "MetaMask uses a third-party service called ", "use_safe_chains_list_validation_desc_2": "to show accurate and standardized network details. This reduces your chances of connecting to malicious or incorrect network. When using this feature, your IP address is exposed to  ", "snaps": {"title": "Snaps", "description": "Overview and manage your snaps", "snap_ui": {"link": {"accessibilityHint": "Opens in a new tab"}}, "snap_settings": {"remove_snap_section_title": "Remove <PERSON>", "remove_snap_section_description": "This action will delete the snap, its data, and its granted permissions.", "remove_button_label": "Remove {{snapName}}", "remove_account_snap_warning": {"title": "Remove <PERSON>", "description": "Removing this Snap removes these accounts from MetaMask:", "remove_account_snap_alert_description_1": "Type", "remove_account_snap_alert_description_2": "to confirm you want to remove this snap:", "banner_title": "Be sure you can access any accounts created by this Snap on your own before removing it", "cancel_button": "Cancel", "continue_button": "Continue", "remove_snap_button": "Remove <PERSON>", "remove_snap_error": "Failed to remove {{snapName}}", "remove_snap_success": "{{snapName}} removed"}}, "snap_details": {"install_date": "Installed on {{date}}", "install_origin": "Install Origin", "enabled": "Enabled", "version": "Version"}, "keyring_account_list_item": {"account_name": "Account name", "public_address": "Public Address"}, "snap_permissions": {"approved_date": "Approved on {{date}}", "permission_section_title": "Permissions", "permission_requested_now": "Requested now", "human_readable_permission_titles": {"endowment:long-running": "Run indefinitely", "endowment:network-access": "Access the internet", "endowment:transaction-insight": "Display transaction insights", "endowment:cronjob": "Schedule and run periodic actions", "endowment:rpc": {"snaps": "Allow other snaps to communicate directly with this snap", "dapps": "Allow dapps to communicate directly with this snap"}, "snap_confirm": "Display custom dialogs", "snap_manageState": "Store and manage data on your device", "snap_notify": "Show notifications", "snap_getBip32Entropy": "Control your {{protocol}} accounts and assets", "snap_getBip32PublicKey": "View your public key for {{protocol}}", "snap_getBip44Entropy": "Control your {{protocol}} accounts and assets", "snap_getEntropy": "Derive arbitrary keys unique to this snap", "endowment:keyring": "Allow requests for adding and controlling Ethereum accounts", "wallet_snap": "Connect to {{otherSnapName}}", "endowment:webassembly": "Support for WebAssembly", "endowment:ethereum-provider": "Access the Ethereum provider", "endowment:unknown": "Unknown permission", "snap_getLocale": "See your preferred language", "endowment:caveat:transaction-origin": "See the origins of websites that suggest transactions", "endowment:extend-runtime": "Extend runtime", "snap_dialog": "Display custom dialogs", "snap_manageAccounts": "Add and control Ethereum accounts", "endowment:signature-insight": "Display signature insights modal", "endowment:protocol": "Provide protocol data for one or more chains", "snap_getPreferences": "See information like your preferred language and fiat currency", "endowment:lifecycle-hooks": "Use lifecycle hooks", "endowment:name-lookup": "Provide domain and address lookups", "endowment:page-home": "Display a custom screen"}}}, "privacy_browser_subheading": "Clear privacy or browser data", "analytics_subheading": "Analytics", "transactions_subheading": "Transactions", "network_provider": "Network provider", "token_nft_ens_subheading": "Token, NFT, and ENS autodetection", "security_check_subheading": "Security checks", "symbol_required": "Symbol is required.", "blockaid_desc": "This feature alerts you to malicious activity by actively reviewing transaction and signature requests.", "security_alerts": "Security alerts", "security_alerts_desc": "This feature alerts you to malicious activity by locally reviewing your transaction and signature requests. Always do your own due diligence before approving any requests. There's no guarantee that this feature will detect all malicious activity. By enabling this feature you agree to the provider's terms of use.", "dismiss_smart_account_update_heading": "Dism<PERSON> \"Switch to Smart Account\" suggestion", "dismiss_smart_account_update_desc": "Turn this on to no longer see the \"Switch to Smart Account\" suggestion on any account. Smart accounts unlocks faster transactions, lower network fees and more flexibility on payment for those.", "use_smart_account_heading": "Use smart account", "use_smart_account_desc": "Keep this on to automatically switch accounts created within MetaMask to smart accounts whenever relevant features are available, such as faster transactions, lower network fees and payment flexibility on payment for those.", "use_smart_account_learn_more": "Learn more.", "smart_transactions_opt_in_heading": "Smart Transactions", "smart_transactions_opt_in_desc_supported_networks": "Turn on Smart Transactions for more reliable and secure transactions on supported networks.", "smart_transactions_learn_more": "Learn more.", "simulation_details": "Estimate balance changes", "simulation_details_description": "Turn this on to estimate balance changes of transactions before you confirm them. This doesn't guarantee the final outcome of your transactions. ", "simulation_details_learn_more": "Learn more.", "aes_crypto_test_form_title": "AES Crypto - Test Form", "aes_crypto_test_form_description": "Section exclusively developed for E2E testing. If this is visible in your app, please report it to MetaMask support.", "developer_options": {"title": "Developer Options", "generate_trace_test": "Generate Trace Test", "generate_trace_test_desc": "Generate a Developer Test Sentry trace."}}, "aes_crypto_test_form": {"generate_random_salt": "Generate Random Salt", "salt_bytes_count": "Salt bytes count", "generate": "Generate", "generate_encryption_key": "Generate encryption key from password", "password": "Password", "salt": "Salt", "encrypt_with_key": "Encrypt with key", "encrypt": "Encrypt", "encryption_key": "Encryption Key", "data": "Data", "decrypt_with_key": "Decrypt with key", "decrypt": "Decrypt"}, "sdk": {"disconnect_title": "Disconnect all sites?", "disconnect_all_info": "If you remove your connections to all sites, you’ll need to give permission to connect again.", "disconnect": "Disconnect", "disconnect_all": "Disconnect all", "disconnect_all_accounts": "Disconnect all Accounts", "manage_connections": "Manage Connections", "manage": "Manage", "cancel": "Cancel", "loading": "Connecting to MetaMask...", "unkown_dapp": "DAPP name not available", "unknown": "Unknown", "no_connections": "No connections", "no_connections_desc": "If you connect an account to a site or an app, you’ll see it here."}, "sdk_session_item": {"connected_accounts": "{{accountsLength}} account(s) connected."}, "sdk_disconnect_modal": {"disconnect_all": "Disconnect from all sites?", "disconnect_all_desc": "If you disconnect your accounts from all sites, you'll need to give permissions to reconnect them.", "disconnect_account": "Disconnect account?", "disconnect_all_accounts": "Disconnect all Accounts", "disconnect_all_accounts_desc": "If you disconnect all accounts from {{dapp}}, you will need to give permissions to reconnect them.", "disconnect_account_desc": "If you disconnect {{account}} from {{dapp}}, you will need to give permissions to reconnect it.", "disconnect_confirm": "Disconnect", "cancel": "Cancel"}, "sdk_return_to_app_modal": {"title": "Return to app", "postNetworkSwitchTitle": "Network successfully switched", "description": "Please return to the app to continue using their services."}, "sdk_feedback_modal": {"ok": "OK", "title": "Account couldn't connect", "info": "Please scan the QR code on the dApp to reconnect to MetaMask"}, "app_information": {"title": "Information", "links": "Links", "privacy_policy": "Privacy Policy", "terms_of_use": "Terms of use", "attributions": "Attributions", "support_center": "Visit our Support Center", "web_site": "Visit our Website", "contact_us": "Contact Us"}, "reveal_credential": {"seed_phrase_title": "Reveal Secret Recovery Phrase", "private_key_title": "Show private key", "show_private_key": "Show private key", "private_key_title_for_account": "Show private key for \"{{accountName}}\"", "cancel": "Cancel", "done": "Done", "confirm": "Next", "seed_phrase_explanation": ["Your", "Secret Recovery Phrase", "gives", "full access to your wallet, funds and accounts.\n\n", "MetaMask is a", "non-custodial wallet.", "That means,", "you are the owner of your Secret Recovery Phrase."], "private_key_explanation": "Save it somewhere safe and secret.", "private_key_warning": "This is the private key for the current selected account: {{accountName}}. Never disclose this key. Anyone with your private key can fully control your account, including transferring away any of your funds.", "seed_phrase_warning_explanation": ["Make sure nobody is looking at your screen. ", "MetaMask Support will never request this."], "private_key_warning_explanation": "Never disclose this key. Anyone with your private key can fully control your account, including transferring away any of your funds.", "reveal_credential_modal": ["Your {{credentialName}} provides ", "full access to your account and funds.\n\nDo not share this with anyone.\n", "full access to your wallet and funds.\n\nDo not share this with anyone.\n", "MetaMask Support will not request this, ", "but phishers might."], "seed_phrase": "Your Secret Recovery Phrase", "private_key": "Your private key", "copy_to_clipboard": "Copy to clipboard", "enter_password": "Enter password to continue", "seed_phrase_copied_ios": "Secret Recovery Phrase temporarily copied to clipboard\n", "seed_phrase_copied_android": "Secret Recovery Phrase copied to clipboard", "seed_phrase_copied_time": "(stored for 1 minute)", "private_key_copied": "Private key temporarily copied to clipboard", "private_key_copied_time": "(stored for 1 minute)", "private_key_copied_ios": "Private key temporarily copied to clipboard\n", "private_key_copied_android": "Private key copied to clipboard\n", "warning_incorrect_password": "Incorrect password", "unknown_error": "Couldn't unlock your account. Please try again.", "hardware_error": "This is a hardware wallet account, you cannot export your private key.", "seed_warning": "This is your wallet's 12 word phrase. This phrase can be used to take control of all of your current and future accounts, including the ability to send away any of their funds. Keep this phrase stored safely, DO NOT share it with anyone.", "text": "TEXT", "qr_code": "QR CODE", "hold_to_reveal_credential": "Hold to reveal {{credentialName}}", "reveal_credential": "Reveal {{credentialName}}", "keep_credential_safe": "Keep your {{credentialName}} safe", "srp_abbreviation_text": "SRP", "srp_text": "Secret Recovery Phrase", "private_key_text": "Private Key", "got_it": "Got it", "learn_more": "Learn More"}, "screenshot_deterrent": {"title": "Safety alert", "description": "Screenshots aren't a safe way to keep track of your {{credentialName}}. Store it somewhere that isn't backed up online to keep your account safe.", "srp_text": "Secret Recovery Phrase", "priv_key_text": "Private Key"}, "password_reset": {"password_title": "Password", "password_desc": "Choose a strong password to unlock MetaMask app on your device. If you lose this password, you will need your Secret Recovery Phrase to re-import your wallet.", "password_learn_more": "Learn more.", "change_password": "Change password", "password_hint": "Password hint"}, "fund_actionmenu": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "deposit_description": "Low-fee bank or card transfer", "buy": "Buy", "buy_description": "Good for buying a specific token", "sell": "Withdraw", "sell_description": "Sell crypto for cash"}, "asset_overview": {"send_button": "Send", "buy_button": "Buy", "token_marketplace": "Token marketplace", "sell_button": "Withdraw", "receive_button": "Receive", "portfolio_button": "Portfolio", "deposit_button": "<PERSON><PERSON><PERSON><PERSON>", "earn_button": "<PERSON><PERSON><PERSON>", "perps_button": "Perps", "add_collectible_button": "Add", "info": "Info", "swap": "<PERSON><PERSON><PERSON>", "bridge": "Bridge", "earn": "<PERSON><PERSON><PERSON>", "disabled_button": {"buy": "Buying not supported for this account", "sell": "Selling not supported for this account", "swap": "Swapping not supported for this account", "deposit": "Depositing not supported for this account", "bridge": "Bridging not supported for this account", "send": "Sending not supported for this account", "action": "This action is not supported for this account", "earn": "Earning not supported for this account", "perps": "Perps trading not supported for this account"}, "description": "Description", "totalSupply": "Total Supply", "address": "Address", "were_unable": "We’re unable to load your", "balance": "balance. See the support article", "troubleshooting_missing": "troubleshooting missing balances", "for_help": "for help.", "troubleshoot": "Troubleshoot", "deposit_description": "Low-fee bank or card transfer", "buy_description": "Good for buying a specific token", "sell_description": "Sell crypto for cash", "swap_description": "Exchange between tokens", "bridge_description": "Transfer tokens between networks", "send_description": "Send crypto to any account", "receive_description": "Receive crypto", "earn_description": "Earn rewards on your tokens", "perps_description": "Trade perp contracts", "chart_time_period": {"1d": "Today", "7d": "Past 7 days", "1w": "Past week", "1m": "Past month", "3m": "Past 3 months", "1y": "Past year", "3y": "Past 3 years", "all": "All"}, "chart_time_period_navigation": {"1d": "1D", "7d": "7D", "1w": "1W", "1m": "1M", "3m": "3M", "1y": "1Y", "3y": "3Y", "all": "All"}, "no_chart_data": {"title": "No chart data", "description": "We could not fetch any data for this token"}, "your_balance": "Your balance", "unable_to_load_balance": "Unable to load your balance", "about": "About", "about_content_display": {"show_more": "Show more", "show_less": "Show less"}, "activity": "{{symbol}} activity", "disclaimer": "Market data is provided by one or more third-party data sources, including CoinGecko. Such third-party content is provided solely for informational purposes and should not be treated as advice to buy, sell, or use any particular asset. MetaMask does not suggest the use of this content for any particular purpose and is not responsible for its accuracy."}, "account_details": {"title": "Account Details", "share_account": "Share", "view_account": "View account on Etherscan", "show_private_key": "Show private key", "account_copied_to_clipboard": "Public address copied to clipboard", "share_public_key": "Sharing my public key: "}, "enable_nft-auto-detection": {"title": "Enable NFT autodetection", "description": "Allow MetaMask to detect and display your NFTs with autodetection. You’ll be able to:", "immediateAccess": "Immediately access your NFTs", "navigate": "Effortlessly navigate your digital assets", "dive": "Dive straight into using your NFTs", "allow": "Allow", "notRightNow": "Not right now"}, "detected_tokens": {"title": "{{tokenCount}} new token found", "title_plural": "{{tokenCount}} new tokens found", "import_cta": "Import ({{tokenCount}})", "hide_cta": "Hide All", "token_address": "Token address: ", "token_lists": "Token lists: {{listNames}}", "token_more": " + {{remainingListCount}} more", "confirm": {"cancel_cta": "Cancel", "confirm_cta": "Confirm", "import": {"title": "Import selected tokens?", "desc": "Only the tokens you've selected will appear in your wallet. You can always import hidden tokens later by searching for them."}, "hide": {"title": "Are you sure?", "desc": "If you hide tokens, they will not be shown in your wallet. However, you can still add them by searching for them."}}, "address_copied_to_clipboard": "Token address copied to clipboard"}, "qr_scanner": {"invalid_qr_code_title": "Invalid QR Code", "invalid_qr_code_message": "The QR code that you are trying to scan it is not valid.", "allow_camera_dialog_title": "Allow camera access", "allow_camera_dialog_message": "We need your permission to scan QR codes", "scanning": "scanning...", "ok": "Ok", "continue": "Continue", "cancel": "Cancel", "error": "Error", "attempting_to_scan_with_wallet_locked": "Looks like you're trying to scan a QR code, you need to unlock your wallet to be able to use it.", "attempting_sync_from_wallet_error": "Looks like you're trying to sync with the extension. In order to do so, you will need to erase your current wallet. \n\nOnce you've erased or reinstalled a fresh version of the app, select the option to \"Sync with MetaMask Extension\". Important! Before erasing your wallet, make sure you've backed up your Secret Recovery Phrase.", "not_allowed_error_title": "Turn on camera access", "not_allowed_error_desc": "To scan a QR code, you'll need to give MetaMask camera access from your device's settings menu.", "unrecognized_address_qr_code_title": "Unrecognized QR Code", "unrecognized_address_qr_code_desc": "Sorry, this QR code is not associated with an account address or a contract address.", "url_redirection_alert_title": "You are about to visit an external link", "url_redirection_alert_desc": "Links can be used to try to defraud or phish people, so make sure to only visit websites that you trust.", "label": "Scan a QR code", "open_settings": "Settings", "camera_not_available": "Camera not available"}, "action_view": {"cancel": "Cancel", "confirm": "Confirm"}, "gas_fee_token_modal": {"title": "Select a token", "title_pay_eth": "Pay with ETH", "native_toggle_wallet": "Pay for network fee using the balance in your wallet.", "list_balance": "Bal:", "insufficient_balance": "Insufficient funds", "native_toggle_metamask": "MetaMask is supplementing the balance to complete this transaction.", "title_pay_with_other_tokens": "Pay with other tokens"}, "transaction": {"transaction_id": "Transaction ID", "alert": "ALERT", "amount": "Amount", "details": "Details", "next": "Next", "back": "Back", "confirm": "Confirm", "reject": "Reject", "edit": "Edit", "cancel": "Cancel", "save": "Save", "speedup": "Speed up", "sign_with_keystone": "Sign with hardware wallet", "sign_with_ledger": "Sign with <PERSON><PERSON>", "from": "From", "gas_fee": "Gas fee", "gas_fee_fast": "FAST", "gas_fee_average": "AVERAGE", "gas_fee_slow": "SLOW", "hex_data": "Hex Data", "custom_nonce": "<PERSON><PERSON>", "custom_nonce_tooltip": "This is the transaction number of an account. Nonce for the first transaction is 0 and it increases in sequential order.", "this_is_an_advanced": "This is an advanced feature used to cancel or speed up any pending transactions.", "current_suggested_nonce": "Current suggested nonce:", "edit_transaction_nonce": "Edit transaction nonce", "think_of_the_nonce": "Think of the nonce as the transaction number of an account. Every account's nonce begins with 0 for the first transaction and continues in sequential order.", "nonce_warning": "Warning: You may encounter issues with future transactions if you continue. Use with caution.", "review_details": "DETAILS", "review_data": "DATA", "data": "Data", "data_description": "Data associated with this transaction", "review_function_type": "FUNCTION TYPE", "review_function": "Function", "review_hex_data": "Hex data", "insufficient": "Insufficient funds", "insufficient_amount": "You need {{amount}} more {{tokenSymbol}} to complete this transaction.", "buy_more_eth": "Buy more ETH", "buy_more": "Buy more", "more_to_continue": "More {{ticker}} is needed to continue", "you_can_also_send_funds": "You can also send funds from another account.", "token_marketplace": "Token marketplace", "token_Marketplace": "Token Marketplace", "go_to_faucet": "Go to faucet", "get_ether": "Get Ether for the {{networkName}} network.", "insufficient_tokens": "Insufficient {{token}}", "invalid_address": "Invalid address", "invalid_from_address": "Invalid sender address", "invalid_amount": "Invalid amount", "invalid_gas": "Invalid gas amount", "invalid_gas_price": "Invalid gas price", "high_gas_price": "Your gas fee may be set unnecessarily high ({{currentGasPrice}}). Consider lowering the amount.", "low_gas_price": "Gas price extremely low", "invalid_collectible_ownership": "You don't own this collectible", "known_asset_contract": "Known asset contract address", "max": "Max", "recipient_address": "Recipient Address", "required": "Required", "to": "To", "total": "Total", "loading": "Loading...", "conversion_not_available": "Conversion rate not available", "value_not_available": "Not Available", "rate_not_available": "Conversion not available", "optional": "Optional", "no_address_for_ens": "No address for ENS name", "lets_try": "Yes, let's try", "approve_warning": "By approving this action, you grant permission for this contract to spend up to", "cancel_tx_title": "Attempt to cancel?", "cancel_tx_message": "Submitting this attempt does not guarantee your original transaction will be cancelled. If the cancellation attempt is successful, you will be charged the transaction fee above.", "speedup_tx_title": "Attempt to speed up?", "speedup_tx_message": "Submitting this attempt does not guarantee your original transaction will be accelerated. If the speed up attempt is successful, you will be charged the transaction fee above.", "nevermind": "Nevermind", "edit_network_fee": "Edit gas fee", "edit_priority": "Edit priority", "gas_cancel_fee": "Gas cancellation Fee", "gas_speedup_fee": "Gas speed up fee", "use_max": "Use max", "set_gas": "Set", "cancel_gas": "Cancel", "transaction_fee_estimated": "Estimated gas fee", "transaction_fee": "Gas fee", "transaction_fee_less": "No fee", "total_amount": "Total amount", "view_data": "View Data", "adjust_transaction_fee": "Adjust transaction fee", "could_not_resolve_ens": "Couldn't resolve ENS", "asset": "<PERSON><PERSON>", "balance": "Balance", "token_id": "Token ID", "not_enough_for_gas": "You have 0 {{ticker}} in your account to pay for transaction fees.", "send": "Send", "confirm_with_qr_hardware": "Confirm with hardware wallet", "confirm_with_ledger_hardware": "Confirm with <PERSON><PERSON>", "confirmed": "Confirmed", "pending": "Pending", "submitted": "Submitted", "failed": "Failed", "cancelled": "Cancelled", "signed": "Pending", "tokenContractAddressWarning_1": "This address is a ", "tokenContractAddressWarning_2": "token contract address", "tokenContractAddressWarning_3": ". If you send tokens to this address, you will lose them.", "smartContractAddressWarning": "This address is a smart contract address. Please make sure you understand what this address is for, otherwise you risk losing your funds.", "continueError": "I understand the risks, continue", "gas_education_title_ethereum": "Ethereum gas fees", "gas_education_title": "Gas fees", "gas_education_1": "Gas fees are paid to crypto miners who process transactions on the ", "gas_education_2_ethereum": "Ethereum network.", "gas_education_2": "network.", "gas_education_3": "MetaMask does not profit from gas fees.", "gas_education_4": "Gas fees fluctuate based on network traffic and transaction complexity.", "gas_education_learn_more": "Learn more about gas fees", "confusable_title": "Check the recipient address", "confusable_msg": "We have detected a confusable character in the ENS name. Check the ENS name to avoid a potential scam.", "similar_to": "is similar to", "contains_zero_width": "contains zero width character", "dapp_suggested_gas": "This gas fee has been suggested by %{origin}. It’s using legacy gas estimation which may be inaccurate. However, editing this gas fee may cause a problem with your transaction. Please reach out to %{origin} if you have questions.", "dapp_suggested_eip1559_gas": "This gas fee has been suggested by %{origin}. Overriding this may cause a problem with your transaction. Please reach out to %{origin} if you have questions.", "address_invalid": "Recipient address is invalid.", "ens_not_found": "No address has been set for this name.", "unknown_qr_code": "Invalid QR code. Please retry.", "invalid_qr_code_sync": "Invalid QR code. Please scan the sync QR code of the hardware wallet.", "no_camera_permission": "Camera not authorized. Please give permission and retry", "invalid_qr_code_sign": "Invalid QR code. Please check your hardware and retry.", "no_camera_permission_android": "You need to grant MetaMask access to your camera to proceed. You may also need to change your system settings.", "mismatched_qr_request_id": "Incongruent transaction data. Please use your hardware wallet to sign the QR code below and tap 'Get Signature'.", "fiat_conversion_not_available": "Fiat conversions are not available at this moment", "hex_data_copied": "Hex data copied to clipboard", "invalid_recipient": "Invalid recipient", "invalid_recipient_description": "Check the address and make sure it's valid", "swap_tokens": "Swap tokens", "fromWithColon": "From:"}, "custom_gas": {"total": "Total", "advanced_options": "Advanced", "basic_options": "Basic", "hide_advanced_options": "<PERSON><PERSON> advanced", "gas_limit": "Gas Limit:", "gas_price": "Gas Price: (GWEI)", "save": "Save", "warning_gas_limit": "Gas limit must be greater than 20999", "warning_gas_limit_estimated": "Estimated gas limit is {{gas}}, use it as minimum value", "cost_explanation": "The gas fee covers the cost of processing your transaction on the Ethereum network. MetaMask does not profit from this fee. The higher the fee the better chances of your transaction getting processed."}, "spend_limit_edition": {"save": "Save", "title": "Edit permission", "spend_limit": "Spend limit permission", "allow": "Allow", "allow_explanation": "to withdraw and spend up to the following amount:", "proposed": "Proposed approval limit", "requested_by": "Spend limit requested by", "custom_spend_limit": "Custom spend limit", "max_spend_limit": "Enter a max spend limit", "minimum": "1.00 {{tokenSymbol}} minimum", "cancel": "Cancel", "approve": "Approve", "allow_to_access": "Give permission to access your", "allow_to_address_access": "Give this address access your", "allow_to_transfer_all": "Allow access to and transfer of all your", "spend_cap": "Spending cap request for your", "token": "token", "nft": "NFT", "you_trust_this_site": "By granting permission, you're allowing the following third party to access your funds.", "you_trust_this_third_party": "This allows a third party to access and transfer your NFTs without further notice until you revoke its access.", "you_trust_this_address": "Do you trust this address? By granting this permission, you're allowing this address to access your funds.", "edit_permission": "Edit permission", "edit": "Edit", "transaction_fee_explanation": "A transaction fee is associated with this permission.", "view_details": "View details", "view_transaction_details": "View transaction details", "view_data": "View Data", "transaction_details": "Transaction Details", "site_url": "Site URL", "permission_request": "Permission request", "details_explanation": "{{host}} may access and spend up to this max amount from this account.", "amount": "Amount:", "allowance": "Allowance:", "to": "To:", "contract": "Contract ({{address}})", "contract_name": "Contract name:", "contract_address": "Contract address:", "invalid_contract_address": " Contract address is invalid. Enter a new one", "contract_allowance": "Allowance:", "data": "Data", "function_approve": "Function: Approve", "function": "Function", "close": "Close", "all_set": "All set!", "all_set_desc": "You’ve successfully set permissions for this site.", "must_be_at_least": "Must be at least {{allowance}}", "access_up_to": "Access up to:", "spending_cap": "Spending cap:", "approve_asset": "Approve asset:"}, "browser": {"title": "Browser", "reload": "Reload", "share": "Share", "bookmark": "Bookmark", "add_to_favorites": "Add to Favorites", "error": "Error", "cancel": "Cancel", "go_back": "Go back", "go_forward": "Go forward", "home": "Home", "close": "Close", "open_in_browser": "Open in browser", "change_url": "Change url", "switch_network": "Switch network", "dapp_browser": "DAPP BROWSER", "dapp_browser_message": "MetaMask is your wallet and browser for the decentralized web. Have a look around!", "featured_dapps": "FEATURED DAPPS", "my_favorites": "MY FAVORITES", "search": "Search or type a URL", "welcome": "Welcome!", "remove": "Remove", "new_tab": "New tab", "tabs_close_all": "Close All", "tabs_done": "Done", "no_tabs_title": "No Open Tabs", "no_tabs_desc": "To browse the decentralized web, add a new tab", "got_it": "Got it", "max_tabs_title": "Maximum tabs reached", "max_tabs_desc": "We currently only support 5 open tabs at once. Please close existing tabs before adding new ones.", "failed_to_resolve_ens_name": "We couldn't resolve that ENS name", "remove_bookmark_title": "Remove favorite", "remove_bookmark_msg": "Do you really want to remove this site from your favorites?", "yes": "Yes", "undefined_account": "Undefined account", "close_tab": "Close tab", "switch_tab": "Switch tab", "protocol_alert_options": {"ignore": "Ignore", "allow": "Allow"}, "protocol_alerts": {"tel": "This website has been blocked from automatically making a phone call", "mailto": "This website has been blocked from automatically composing an email.", "generic": "This website has been blocked from automatically opening an external application"}, "ipfs_gateway_off_title": "IPFS gateway is off", "ipfs_gateway_off_content": "To see this site, turn on IPFS gateway in Privacy and Security Settings."}, "backup_alert": {"title": "Protect your wallet", "left_button": "Remind me later", "right_button": "Protect wallet"}, "add_favorite": {"title": "Add Favorite", "title_label": "Name", "url_label": "Url", "add_button": "Add", "cancel_button": "Cancel"}, "approval": {"title": "Confirm Transaction"}, "approve": {"title": "Approve", "deeplink": "Deeplink", "qr_code": "QR Code"}, "transactions": {"tx_review_confirm": "Confirm", "tx_review_transfer": "Transfer", "tx_review_contract_deployment": "Contract Deployment", "tx_review_transfer_from": "Transfer From", "tx_review_unknown": "Unknown Method", "tx_review_approve": "Approve", "tx_review_increase_allowance": "Increase Allowance", "tx_review_set_approval_for_all": "Set Approval For All", "tx_review_staking_claim": "Staking Claim", "tx_review_staking_deposit": "Staking Deposit", "tx_review_staking_unstake": "Unstake", "tx_review_lending_deposit": "Lending Deposit", "tx_review_lending_withdraw": "Lending With<PERSON>wal", "tx_review_perps_deposit": "Funded Perps account", "sent_ether": "<PERSON><PERSON>", "self_sent_ether": "Sent Yourself Ether", "received_ether": "Received <PERSON>", "sent_dai": "Sent DAI", "self_sent_dai": "Sent Yourself DAI", "received_dai": "Received DAI", "sent_tokens": "<PERSON><PERSON>", "ether": "<PERSON><PERSON>", "sent_unit": "Sent {{unit}}", "self_sent_unit": "Sent Yourself {{unit}}", "received_unit": "Received {{unit}}", "sent_collectible": "Sent Collectible", "sent": "<PERSON><PERSON>", "received": "Received", "receive": "Receive", "swap": "<PERSON><PERSON><PERSON>", "send": "Send", "redeposit": "Redeposit", "interaction": "Interaction", "contract_deploy": "Contract Deployment", "to_contract": "New Contract", "tx_details_free": "Free", "tx_details_not_available": "Not available", "smart_contract_interaction": "Smart contract interaction", "swaps_transaction": "Swaps transaction", "bridge_transaction": "Bridge", "approve": "Approve", "increase_allowance": "Increase Allowance", "set_approval_for_all": "Set Approval For All", "hash": "Hash", "from": "From", "to": "To", "details": "Details", "amount": "Amount", "fee": {"transaction_fee_in_ether": "Transaction Fee", "transaction_fee_in_usd": "Transaction Fee (USD)"}, "gas_used": "Gas Used (Units)", "gas_limit": "Gas Limit (Units)", "gas_price": "Gas Price (GWEI)", "base_fee": "Base Fee (GWEI)", "priority_fee": "Priority Fee (GWEI)", "multichain_priority_fee": "Priority Fee", "max_fee": "<PERSON> Fee Per Gas", "total": "Total", "view_on": "View on", "view_on_etherscan": "View on Etherscan", "view_full_history_on": "View full history on", "view_full_history_on_etherscan": "View full history on Etherscan", "hash_copied_to_clipboard": "Transaction hash copied to clipboard", "address_copied_to_clipboard": "Address copied to clipboard", "transaction_error": "Transaction error", "address_to_placeholder": "Search, public address (0x), or ENS", "address_from_balance": "Balance:", "status": "Status", "date": "Date", "nonce": "<PERSON><PERSON>", "from_device_label": "from this device", "import_wallet_row": "Account added to this device", "import_wallet_label": "Account Added", "import_wallet_tip": "All future transactions made from this device will include a label \"from this device\" next to the timestamp. For transactions dated before adding the account, this history will not indicate which outgoing transactions originated from this device.", "sign_title_scan": "<PERSON><PERSON> ", "sign_title_device": "with your hardware wallet", "sign_description_1": "After you have signed with your hardware wallet,", "sign_description_2": "tap on Get Signature", "sign_get_signature": "Get Signature", "transaction_id": "Transaction ID", "network": "Network", "request_from": "Request from", "network_fee": "Network Fee", "network_fee_tooltip": "Amount paid to process the transaction on network.", "smart_account_upgrade": "Upgrade to smart account", "smart_account_downgrade": "Switch to standard account", "batched_transactions": "Batched transactions", "gas_modal": {"edit_network_fee": "Edit network fee", "advanced_gas_fee": "Advanced network fee", "site_suggested": "Site suggested", "advanced": "Advanced", "low": "Low", "medium": "Market", "high": "Aggressive", "network_suggested": "Network suggested", "gas_limit": "Gas Limit", "save": "Save", "max_base_fee": "Max Base Fee", "priority_fee": "Priority Fee", "current_priority_fee": "Current: {{min}} - {{max}} GWEI", "historical_priority_fee": "12 hr: {{min}} - {{max}} GWEI", "estimated_base_fee": "Current: {{value}} GWEI", "historical_base_fee": "12 hr: {{min}} - {{max}} GWEI", "gas_price": "Gas price", "field_required": "{{field}} is required", "max_base_fee_required": "Max base fee is required", "gas_price_required": "Gas price is required", "priority_fee_required": "Priority fee is required", "gas_limit_required": "Gas limit is required", "only_numbers_allowed": "Only numbers are allowed", "negative_values_not_allowed": "Negative values are not allowed", "max_base_fee_must_be_greater_than_priority_fee": "Max base fee must be greater than priority fee", "gas_limit_too_low": "Gas limit must be greater than 21000", "priority_fee_too_high": "Priority fee must be less than max base fee", "no_zero_value": "{{field}} must be greater than 0", "speed": "Speed", "only_integers_allowed": "Only whole numbers are allowed"}}, "smart_transactions": {"status_submitting_header": "Submitting your transaction", "status_submitting_description": "Estimated completion {{timeLeft}}", "status_success_header": "Your transaction is complete", "status_submitting_past_estimated_deadline_header": "Sorry for the wait", "status_submitting_past_estimated_deadline_description": "If your transaction is not finalized within {{timeLeft}}, it will be canceled and you will not be charged for gas.", "status_cancelled_header": "Your transaction was canceled", "status_cancelled_description": "Your transaction couldn't be completed, so it was canceled to save you from paying unnecessary gas fees.", "status_failed_header": "Your transaction failed", "status_failed_description": "Sudden market changes can cause failures. If the problem continues, reach out to MetaMask customer support.", "view_transaction": "View transaction", "view_activity": "View activity", "return_to_dapp": "Return to {{dappName}}", "try_again": "Try again", "create_new": "Create new {{txType}}", "swap": "swap", "send": "send"}, "address_book": {"recents": "Recents", "save": "Save", "delete_contact": "Delete contact", "delete": "Delete", "cancel": "Cancel", "add_to_address_book": "Add to address book", "enter_an_alias": "Enter an alias", "add_this_address": "Add this address to your address book", "next": "Next", "enter_an_alias_placeholder": "e.g. <PERSON>lik B.", "add_contact_title": "Add Contact", "add_contact": "Add contact", "edit_contact_title": "Edit Contact", "edit_contact": "Edit contact", "edit": "Edit", "address_already_saved": "Contact already saved", "address": "Address", "name": "Name", "nickname": "Name", "add_input_placeholder": "Public address (0x), or ENS", "between_account": "Transfer between my accounts", "others": "Others", "memo": "Memo", "network": "Network"}, "duplicate_address": {"title": "This is a duplicate address", "body": "Your contact list shows this address on more than one chain. Be sure to select the correct contact before you send any funds.", "button": "Got it"}, "transaction_submitted": {"title": "Transaction Submitted", "your_tx_hash_is": "Your transaction hash is:", "view_on_etherscan": "View on Etherscan"}, "networks": {"title": "Networks", "other_networks": "Other Networks", "close": "Close", "status_ok": "All Systems Operational", "status_not_ok": "The network is having some issues", "want_to_add_network": "Want to add this network?", "add_custom_network": "Add custom network", "network_infomation": "This allows this network to be used within MetaMask", "network_endorsement": "MetaMask does not endorse custom networks or their security.", "learn_about": "Learn about", "network_risk": "scams and network security risks", "network_display_name": "Display name", "network_chain_id": "Chain ID", "network_rpc_url": "Network URL", "network_rpc_url_label": "Network RPC URL", "network_rpc_url_warning_punycode": "Attackers sometimes mimic sites by making small changes to the site address. Make sure you're interacting with the intended Network URL before you continue. Punycode version:", "new_default_network_url": "New default network URL", "current_label": "Current", "new_label": "New", "review": "Review", "view_details": "View details", "network_details": "Network details", "network_select_confirm_use_safe_check": "Selecting Confirm turns on Network details check. You can turn off Network details check in ", "network_settings_security_privacy": "Settings > Security and privacy", "network_currency_symbol": "Currency Symbol", "network_block_explorer_url": "Block Explorer URL", "search": "Search for previously added network", "search-short": "Search", "add": "Add", "continue": "Continue", "cancel": "Cancel", "approve": "Approve", "update": "Update", "edit_network_details": "Edit network details", "malicious_network_warning": "A malicious network provider can lie about the state of the blockchain and record your network activity. Only add custom networks you trust.", "security_link": "https://support.metamask.io/networks-and-sidechains/managing-networks/user-guide-custom-networks-and-sidechains/", "network_warning_title": "Network Information", "additional_network_information_title": "Additional Networks Information", "network_warning_desc": "This network connection relies on third parties. This connection may be less reliable or enable third-parties to track activity.", "additonial_network_information_desc": "Some of these networks rely on third parties. The connections may be less reliable or enable third-parties to track activity.", "connect_more_networks": "Connect more networks", "learn_more": "Learn more", "learn_more_url": "https://support.metamask.io/networks-and-sidechains/managing-networks/the-risks-of-connecting-to-an-unknown-network/", "switch_network": "Switch to network", "switch": "Switch", "select_all": "Select all", "deselect_all": "Deselect all", "new_network": "New network added", "network_name": "{{networkName}} Network", "network_added": " is now available in the network selector.", "provider": "A provider is trusted to tell your wallet its balances and to broadcast its transactions faithfully", "no_match": "No matching results found.", "empty_popular_networks": "You’ve added all the popular networks. You can discover more networks", "add_other_network_here": "here.", "you_can": "Or you can", "add_network": "add more networks manually.", "add_specific_network": "Add {{network_name}}", "update_network": "Update {{network_name}}", "select_network": "Select a network", "enabled_networks": "Enabled networks", "additional_networks": "Additional networks", "all_popular_networks": "All popular networks", "show_test_networks": "Show test networks", "deprecated_goerli": "Due to the protocol changes of Ethereum: Goerli test network may not work as reliably and will be deprecated soon.", "network_deprecated_title": "This network is deprecated", "network_deprecated_description": "The network you're trying to connect to is no longer supported on Metamask.", "edit_networks_title": "Edit networks"}, "permissions": {"title_this_site_wants_to": "This site wants to:", "title_dapp_url_wants_to": "{{dappUrl}} wants to:", "title_dapp_url_has_approval_to": "{{dappUrl}} has approval to:", "use_enabled_networks": "Use your enabled networks", "wants_to_see_your_accounts": "See your accounts and suggest transactions", "requesting_for": "Requesting for ", "requesting_for_accounts": "Requesting for {{numberOfAccounts}} accounts", "requesting_for_networks": "Requesting for {{numberOfNetworks}} networks", "n_networks_connect": "{{numberOfNetworks}} networks connected ", "network_connected": "network connected ", "see_your_accounts": "See your accounts and suggest transactions", "connected_to": "Connected to ", "manage_permissions": "Manage Permissions", "edit": "Edit", "cancel": "Cancel", "got_it": "Got it", "connection_details_title": "Connection Details", "connection_details_description": "You connected to this site using the MetaMask browser on {{connectionDateTime}}", "title_add_network_permission": "Add network permission", "add_this_network": "Add this network", "permitted_networks": "Permitted networks", "choose_from_permitted_networks": "Choose from permitted networks", "this_site_cant": "This site can't be used with your current network. Add network permissions or choose an already permitted network.", "non_permitted_network_description": "This site can't be used with your current network. Add network permissions or choose an already permitted network.", "edit_permissions": "Edit permissions", "permitted_networks_info_sheet_description": "This is a list of networks that you've previously granted permissions to use on this site. Select one from the list or edit network permissions for this site.", "connect_an_account": "Connect an account", "sdk_connection": "SDK {{originator_platform}} v{{api_version}}"}, "account_dapp_connections": {"account_summary_header": "Connect this website with MetaMask"}, "select": {"cancel": "Cancel", "done": "Done"}, "signature_request": {"title": "Signature Request", "cancel": "Cancel", "sign": "Sign", "sign_requested": "Your signature is being requested", "signing": "Sign this message?", "account_title": "Account:", "balance_title": "Balance:", "message": "Message", "message_from": "Message from", "learn_more": "Learn more", "read_more": "Read more"}, "watch_asset_request": {"title": "Add Suggested Token", "cancel": "CANCEL", "add": "ADD TOKEN", "message": "Would you like to add this token?", "token": "Token", "balance": "Balance"}, "unit": {"eth": "ETH", "sai": "SAI", "dai": "DAI", "negative": "-", "divisor": "/", "token_id": "#", "colon": ":", "point": ".", "week": "week", "day": "day", "hour": "hr", "minute": "min", "second": "sec", "empty_data": "0x"}, "biometrics": {"enable_touchid": "Unlock with Touch ID?", "enable_faceid": "Unlock with Face ID?", "enable_fingerprint": "Unlock with Fingerprint?", "enable_biometrics": "Unlock with Biometrics?", "enable_device_passcode_ios": "Unlock with device passcode?", "enable_device_passcode_android": "Unlock with device PIN?"}, "authentication": {"auth_prompt_title": "Authentication required", "auth_prompt_desc": "Please authenticate in order to use MetaMask", "fingerprint_prompt_title": "Authentication required", "fingerprint_prompt_desc": "Use your fingerprint to unlock MetaMask", "fingerprint_prompt_cancel": "Cancel"}, "accountApproval": {"title": "CONNECT REQUEST", "walletconnect_title": "WALLETCONNECT REQUEST", "action": "Connect to this site?", "action_reconnect": "To resume connection, choose the number you see on the dApp", "action_reconnect_deeplink": "Do you want to reconnect to this dApp?", "connect": "Connect", "resume": "Resume", "cancel": "Cancel", "donot_rememberme": "Do not remember this dApp connection", "disconnect": "Disconnect", "permission": "View your", "address": "public address", "sign_messages": "Sign messages", "on_your_behalf": "on your behalf", "warning": "By clicking connect, you allow this dapp to view your public address. This is an important security step to protect your data from potential phishing risks."}, "import_private_key": {"title": "Import Account", "description_one": "Imported private keys are backed up to your account and sync automatically when you sign in with the same Google or Apple login.", "description_srp": "Imported accounts are viewable in your wallet but are not recoverable with your MetaMask Secret Recovery Phrase.", "learn_more": "Learn more", "learn_more_here": "about how imported keys work.", "learn_more_srp": "Learn more about imported accounts", "here": "here.", "subtitle": "Paste your private key string", "cta_text": "Import", "example": "e.g.\n3a1076bf45ab87712ad64ccb3b10217737f7faacbf2872e88fdd9a537d8fe266", "error_title": "Something went wrong", "error_message": "We couldn't import that private key. Please make sure you entered it correctly.", "error_empty_message": "You need to enter your private key.", "or_scan_a_qr_code": "or Scan a QR Code"}, "import_private_key_success": {"title": "Account successfully imported!", "description_one": "You'll now be able to view your account in MetaMask."}, "import_new_secret_recovery_phrase": {"title": "Import Secret Recovery Phrase", "description": "Enter your wallet's secret recovery phrase. You can import any Ethereum, Solana, or Bitcoin secret recovery phrase.", "subtitle": "Paste your secret recovery phrase", "cta_text": "Continue", "paste": "Paste", "clear": "Clear all", "srp_number_of_words_option_title": "Number of words", "12_word_option": "I have a 12 word phrase", "24_word_option": "I have a 24 word phrase", "error_title": "Something went wrong", "error_message": "We couldn't import that secret recovery phrase. Please make sure you entered it correctly.", "error_empty_message": "You need to enter your secret recovery phrase.", "error_number_of_words_error_message": "Secret Recovery Phrases contain 12, or 24 words", "error_srp_is_case_sensitive": "Invalid input! Secret Recovery Phrase is case sensitive.", "error_srp_word_error_1": "Word ", "error_srp_word_error_2": " is incorrect or misspelled.", "error_multiple_srp_word_error_1": "Word ", "error_multiple_srp_word_error_2": " and ", "error_multiple_srp_word_error_3": " are incorrect or misspelled.", "error_invalid_srp": "Invalid Secret Recovery Phrase", "error_duplicate_srp": "This Secret Recovery Phrase has already been imported.", "success_1": "Secret Recovery Phrase", "success_2": "imported"}, "first_incoming_transaction": {"title": "{{asset}} was deposited in your account", "amount": "Amount:", "account": "Account:", "from": "From:", "cta_text": "OK"}, "secure_your_wallet": {"title": "Secure your wallet", "step_1": "Step 1:", "step_1_description": "Create password", "step_2": "Step 2:", "step_2_description": "Save wallet Secret Recovery Phrase", "info_text_1": "Take a few moments to finish setting up your MetaMask wallet.", "info_text_2": "This will ensure only you can access your funds and will let you recover your wallet if you lose your device", "cta_text": "Create Password", "creating_password": "Creating password...", "srp_list_selection": "Select Secret Recovery Phrase"}, "account_backup_step_1": {"remind_me_later": "Remind me later", "remind_me_later_subtext": "(Not recommended)", "title": "Secure your wallet", "info_text_1_1": "Don’t risk losing your funds. Protect your wallet by saving your", "info_text_1_2": "Secret Recovery Phrase", "info_text_1_3": "in a place you trust.", "info_text_1_4": "It’s the only way to recover your wallet if you get locked out of the app or get a new device.", "cta_text": "Get started", "cta_subText": "Highly recommended", "skip_button_cancel": "Secure now", "skip_button_confirm": "<PERSON><PERSON>", "skip_title": "Skip account security?", "skip_check": "If you lose this Secret Recovery Phrase, you won’t be able to access this wallet.", "what_is_seedphrase_title": "What’s a Secret Recovery Phrase?", "what_is_seedphrase_text_1": "A Secret Recovery Phrase, also called a seed phrase or mnemonic, is a set of words that lets you access and control your crypto wallet. To move your wallet to MetaMask, you need this phrase.", "what_is_seedphrase_text_2": "You must keep your Secret Recovery Phrase secret and safe. If someone gets your Secret Recovery Phrase, they’ll gain control over your accounts.", "what_is_seedphrase_text_3": "Save it in a place where only you can access it. If you lose it, not even MetaMask can help you recover it.", "what_is_seedphrase_text_4": "Anyone with your Secret Recovery Phrase can:", "seedPhrase_point_1": "Take all your money", "seedPhrase_point_2": "Confirm transactions", "seedPhrase_point_3": "Change your login information", "what_is_seedphrase_confirm": "Got it"}, "account_backup_step_1B": {"title": "Secure your wallet", "subtitle_1": "Secure your wallet's", "subtitle_2": "Secret Recovery Phrase.", "cta_text": "Start", "learn_more": "Learn More", "why_important": "Why is it important?", "manual_title": "Manual", "manual_subtitle": "Write down your Secret Recovery Phrase on a piece of paper and store in a safe place.", "manual_security": "Security level: Very strong", "risks_title": "Risks are:", "risks_1": "You lose it", "risks_2": "You forget where you put it", "risks_3": "Someone else finds it", "other_options": "Other options: Doesn’t have to be paper!", "tips_title": "Tips:", "tips_1": "Store in bank vault", "tips_2": "Store in a safe", "tips_3": "Store in multiple secret places", "why_secure_title": "Protect your wallet", "why_secure_1": "Don’t risk losing your funds. Protect your wallet by saving your Secret Recovery Phrase in a place you trust.", "why_secure_2": " It’s the only way to recover your wallet if you get locked out of the app or get a new device."}, "account_backup_step_2": {"cancel_backup_title": "Cancel Backup", "cancel_backup_message": "We highly recommend you save your Secret Recovery Phrase in order to restore your wallet.", "cancel_backup_ok": "Yes, I'll take the risk", "cancel_backup_no": "No, back up Secret Recovery Phrase", "title": "Grab a pen and paper", "info": "Next step is to write your Secret Recovery Phrase down.", "info_2_1": "You will be asked to ", "info_2_2": "re-enter", "info_2_3": " it for confirmation", "cta_text": "OK"}, "account_backup_step_3": {"cancel_backup_title": "Cancel Backup", "cancel_backup_message": "We highly recommend you save your Secret Recovery Phrase in order to restore your wallet.", "cancel_backup_ok": "Yes, I'll take the risk", "cancel_backup_no": "No, back up Secret Recovery Phrase", "title": "Is anyone watching?", "info_text": "Make sure no other human or robot is watching your screen. If your Secret Recovery Phrase is copied, it can be used on other devices to steal your funds", "cta_text": "NO ONE'S WATCHING ME"}, "account_backup_step_4": {"cancel_backup_title": "Cancel Backup", "cancel_backup_message": "We highly recommend you save your Secret Recovery Phrase in order to restore your wallet.", "cancel_backup_ok": "Yes, I'll take the risk", "cancel_backup_no": "No, back up Secret Recovery Phrase", "back": "Back", "title": "Your Secret Recovery Phrase", "info_text_1": "Carefully write down these words on paper. Their order matters.", "info_text_2": "You'll be asked to re-enter it on the next screen", "cta_text": "I'VE COPIED THE PHRASE", "confirm_password": "Confirm your password", "before_continiuing": "Before continuing we need you to confirm your password", "confirm": "CONFIRM"}, "account_backup_step_5": {"error_title": "Oops!", "error_message": "The order of the words is incorrect. Please make sure you wrote it down correctly and go to the previous screen if it is necessary", "back": "Back", "title": "Confirm Secret Recovery Phrase", "info_text": "Insert each word in the order it was presented to you on the previous screen.", "cta_text": "CONFIRM PHRASE", "modal_title": "Secret Recovery Phrase confirmed!", "modal_text": "This was to ensure you follow this security measure", "modal_button": "NEXT"}, "account_backup_step_6": {"title": "Security Tips", "info_text": "MetaMask cannot recover your Secret Recovery Phrase if you lose it", "tip_1": "Keep multiple backups of your Secret Recovery Phrase", "tip_2": "Store the phrase in a trusted password manager and paper backups in a safe place", "tip_3": "Never share this phrase with anyone", "disclaimer": "* You can find your Secret Recovery Phrase if you go to ", "disclaimer_bold": "Settings > Security & Privacy", "cta_text": "GOT IT!", "modal_title": "Congratulations!", "modal_text": "You are all backed up and ready to go!", "modal_button": "DONE", "copy_seed_phrase": "COPY Secret Recovery Phrase TO CLIPBOARD"}, "manual_backup": {"progressOne": "Create Password", "progressTwo": "Secure wallet", "progressThree": "Confirm Secret Recovery Phrase"}, "manual_backup_step_1": {"steps": "Step {{currentStep}} of {{totalSteps}}", "action": "Save your Secret Recovery Phrase", "info-1": "This is your", "info-2": "Secret Recovery Phrase.", "info-3": "Write it down in the correct order and keep it safe. If someone has your Secret Recovery Phrase, they can access your wallet.", "info-4": "Don’t share it with anyone, ever.", "continue": "Continue", "reveal": "Tap to reveal", "watching": "Make sure no one is watching your screen.", "view": "View", "confirm_password": "Confirm your password", "before_continiuing": "Enter password to continue", "enter_current_password": "Enter your current password", "confirm": "Confirm", "got_it": "Got it", "learn_more": "Learn More", "password": "Password"}, "manual_backup_step_2": {"steps": "Step {{currentStep}} of {{totalSteps}}", "action": "Confirm your Secret Recovery Phrase", "info": "Select the missing words in the correct order.", "complete": "Complete Backup", "success": "Success", "error-title": "Not quite right", "error-description": "Double-check your Secret Recovery Phrase and try again.", "success-title": "Perfect!", "success-description": "That’s right! And remember: never share this phrase with anyone, ever.", "success-button": "Got it", "error-button": "Try again", "continue": "Continue"}, "manual_backup_step_3": {"steps": "Step {{currentStep}} of {{totalSteps}}", "congratulations": "Congratulations", "success": "You’ve successfully protected your wallet. Remember to keep your Secret Recovery Phrase safe, it's your responsibility!", "hint": "Leave yourself a hint?", "recover": "MetaMask cannot recover your wallet should you lose it. You can find your Secret Recovery Phrase in Settings > Security & Privacy.", "learn": "Learn more", "done": "Done", "recovery_hint": "Recovery hint", "leave_hint": "Leave yourself a hint. Write the location / where you saved it to remind yourself how you can access it. This information does not leave your device.", "no_seedphrase": "Do not use this to write your Secret Recovery Phrase.", "example": "e.g. <PERSON>'s house", "save": "Save", "cancel": "Cancel"}, "phishing": {"ethereum_phishing_detection": "Ethereum Phishing Detection", "ethereum_phishing_detector": "Ethereum Phishing Detector", "intro": " is currently on the MetaMask domain warning list. This means that based on information available to us, MetaMask believes this domain could currently compromise your security and, as an added safety feature, MetaMask has restricted access to the site. To override this, please read the rest of this warning for instructions on how to continue at your own risk.", "reasons": "There are many reasons sites can appear on our warning list, and our warning list compiles from other widely used industry lists. Such reasons can include known fraud or security risks, such as domains that test positive on the ", "list_content": "Domains on these warning lists may include outright malicious websites and legitimate websites that have been compromised by a malicious actor.", "to_read_more": "To read more about this site ", "review_on_etherscam": "please review the domain on Etherscam.", "warning": "Note that this warning list is compiled on a voluntary basis. This list may be inaccurate or incomplete. Just because a domain does not appear on this list is not an implicit guarantee of that domain's safety. As always, your transactions are your own responsibility. If you wish to interact with any domain on our warning list, you can do so by ", "continue_on_your_own": "continuing at your own risk.", "file_an_issue_intro": "If you think this domain is incorrectly flagged or if a blocked legitimate website has resolved its security issues, ", "file_an_issue": "please file an issue.", "back_to_safety": "Back to safety", "site_might_be_harmful": "This website might be harmful", "metamask_flagged_site": "MetaMask flagged the site you're trying to visit as potentially deceptive. Attackers may trick you into doing something dangerous.", "you_may_proceed_anyway": "You may also", "proceed_anyway": "proceed anyway", "but_please_do_so_at_your_own_risk": "but please do so at your own risk.", "report_detection_problem": "Report a detection problem", "share_on_twitter": "If you found this helpful, share on <PERSON>!", "share_text": "MetaMask protected me from visiting a potentially harmful site: {{url}}. Stay safe!"}, "notifications": {"timeout": "Timeout", "no_date": "Unknown", "yesterday": "Yesterday", "staked": "Staked", "received": "Received", "unstaked": "Unstaked", "mark_all_as_read": "Mark all as read", "to": "To", "rate": "Rate (fees included)", "unstaking_requested": "Unstaking requested", "stake_completed": "Stake completed", "withdrawal_completed": "Withdrawal completed", "unstake_completed": "Unstake completed", "withdrawal_requested": "<PERSON><PERSON><PERSON> requested", "stake_ready_to_be_withdrawn": "Stake ready to be withdrawn", "swap_completed": "Swapped {{from}} for {{to}}", "swap": "Swapped", "sent": "Sent to {{address}}", "menu_item_title": {"metamask_swap_completed": "Swapped {{symbolIn}} for {{symbolOut}}", "erc20_sent": "Sent to {{address}}", "erc20_received": "Received from {{address}}", "eth_sent": "Sent to {{address}}", "eth_received": "Received from {{address}}", "erc721_sent": "Sent NFT to {{address}}", "erc1155_sent": "Sent NFT to {{address}}", "erc721_received": "Received NFT from {{address}}", "erc1155_received": "Received NFT from {{address}}", "rocketpool_stake_completed": "Staked", "rocketpool_unstake_completed": "Unstaking complete", "lido_stake_completed": "Staked", "lido_withdrawal_requested": "Unstaking requested", "lido_withdrawal_completed": "Unstaking complete", "lido_stake_ready_to_be_withdrawn": "<PERSON><PERSON><PERSON> requested"}, "menu_item_description": {"lido_withdrawal_requested": "Your request to unstake {{amount}} {{symbol}} has been sent", "lido_stake_ready_to_be_withdrawn": "You can now withdraw your unstaked {{symbol}}"}, "modal": {"title_sent": "Sent {{symbol}}", "title_received": "Received {{symbol}}", "title_unstake_requested": "Unstaking requested", "title_untake_ready": "Withdrawl ready", "title_stake": "Staked {{symbol}}", "title_unstake_completed": "Unstaking completed", "title_swapped": "Swapped {{symbolIn}} to {{symbolOut}}", "label_address_to": "To", "label_address_from": "From", "label_address_to_you": "To (You)", "label_address_from_you": "From (You)", "label_asset": "<PERSON><PERSON>", "label_account": "Account", "label_unstaking_requested": "Unstaking Requested", "label_unstake_ready": "Withdrawl ready", "label_staked": "Staked", "label_received": "Received", "label_unstaking_confirmed": "Unstaking Confirmed", "label_swapped": "Swapped", "label_to": "To"}, "received_from": "Received {{amount}} {{ticker}} from {{address}}", "nft_sent": "Sent NFT to {{address}}", "erc721_sent": "Sent NFT to {{address}}", "erc1155_sent": "Sent NFT to {{address}}", "erc721_received": "Received NFT from {{address}}", "erc1155_received": "Received NFT from {{address}}", "received_nft": "Received NFT from {{address}}", "pending_title": "Transaction submitted", "pending_deposit_title": "Deposit in progress!", "pending_withdrawal_title": "Withdrawal in progress!", "cancelled_title": "Transaction cancelled!", "success_title": "Transaction #{{nonce}} Complete!", "speedup_title": "Speeding up #{{nonce}}!", "success_deposit_title": "Deposit Complete!", "success_withdrawal_title": "Withdrawal Complete!", "error_title": "Oops, something went wrong :/", "received_title": "You received {{amount}} {{assetType}}", "metamask_swap_completed_title": "Swap completed", "erc20_sent_title": "Funds sent", "erc20_received_title": "Funds received", "eth_sent_title": "Funds sent", "eth_received_title": "Funds received", "rocketpool_stake_completed_title": "Stake complete", "rocketpool_unstake_completed_title": "Unstake complete", "lido_stake_completed_title": "Stake complete", "lido_withdrawal_requested_title": "<PERSON><PERSON><PERSON> requested", "lido_withdrawal_completed_title": "Withdrawal completed", "lido_stake_ready_to_be_withdrawn_title": "Stake ready for withdrawal", "erc721_sent_title": "NFT sent", "erc721_received_title": "NFT received", "erc1155_sent_title": "NFT sent", "erc1155_received_title": "NFT received", "default_message_title": "MetaMask", "default_message_description": "Tap to view", "received_payment_title": "Instant payment received", "pending_message": "Waiting for confirmation", "pending_deposit_message": "Waiting for deposit to complete", "pending_withdrawal_message": "Waiting for withdrawal to complete", "error_message": "Tap to view this transaction", "error_retrieving_fcm_token": "Error while getting and saving FCM token", "error_fcm_not_found": "getFCMToken: No FCM token found", "error_checking_permission": "Error checking if a user has push notifications permission", "success_message": "Tap to view this transaction", "speedup_message": "Trying to speed up the transaction", "success_deposit_message": "Your funds are ready to use", "success_withdrawal_message": "Your funds have been moved to your wallet", "cancelled_message": "Tap to view this transaction", "received_message": "Tap to view this transaction", "received_payment_message": "You received {{amount}} DAI", "eth_received_message": "You received some ETH", "metamask_swap_completed_message": "Swapped {{symbolIn}} for {{symbolOut}}", "erc20_sent_message": "Sent to {{address}}", "erc20_received_message": "Received from {{address}}", "eth_sent_message": "Sent to {{address}}", "rocketpool_stake_completed_message": "Staked", "rocketpool_unstake_completed_message": "Unstaking complete", "lido_stake_completed_message": "Staked", "lido_withdrawal_requested_message": "Unstaking requested", "lido_withdrawal_completed_message": "Unstaking complete", "lido_stake_ready_to_be_withdrawn_message": "<PERSON><PERSON><PERSON> requested", "push_notification_content": {"funds_sent_title": "Funds sent", "funds_sent_description": "You successfully sent {{amount}} {{symbol}}", "funds_sent_default_description": "You successfully sent some tokens", "funds_received_title": "Funds received", "funds_received_description": "You received {{amount}} {{symbol}}", "funds_received_default_description": "You received some tokens", "metamask_swap_completed_title": "Swap completed", "metamask_swap_completed_description": "Your MetaMask Swap was successful", "nft_sent_title": "NFT sent", "nft_sent_description": "You have successfully sent an NFT", "nft_received_title": "NFT received", "nft_received_description": "You received new NFTs", "rocketpool_stake_completed_title": "Stake complete", "rocketpool_stake_completed_description": "Your RocketPool stake was successful", "rocketpool_unstake_completed_title": "Unstake complete", "rocketpool_unstake_completed_description": "Your RocketPool unstake was successful", "lido_stake_completed_title": "Stake complete", "lido_stake_completed_description": "Your Lido stake was successful", "lido_stake_ready_to_be_withdrawn_title": "Stake ready for withdrawal", "lido_stake_ready_to_be_withdrawn_description": "Your Lido stake is now ready to be withdrawn", "lido_withdrawal_requested_title": "<PERSON><PERSON><PERSON> requested", "lido_withdrawal_requested_description": "Your Lido withdrawal request was submitted", "lido_withdrawal_completed_title": "Withdrawal completed", "lido_withdrawal_completed_description": "Your Lido withdrawal was successful", "perps_position_liquidated_title": "Position liquidated", "perps_position_liquidated_description_long": "Your {{symbol}} long was closed.", "perps_position_liquidated_description_short": "Your {{symbol}} short was closed.", "perps_stop_loss_triggered_title": "Stop loss triggered", "perps_stop_loss_triggered_description_long": "Your {{symbol}} long closed at your stop loss.", "perps_stop_loss_triggered_description_short": "Your {{symbol}} short closed at your stop loss.", "perps_take_profit_triggered_title": "Take profit triggered", "perps_take_profit_triggered_description_long": "Your {{symbol}} long closed at your take profit.", "perps_take_profit_triggered_description_short": "Your {{symbol}} short closed at your take profit.", "perps_limit_order_filled_title": "Limit order filled", "perps_limit_order_filled_description_long": "Your {{symbol}} long position is now open.", "perps_limit_order_filled_description_short": "Your {{symbol}} short position is now open."}, "prompt_title": "Receive Push Notifications", "notifications_enabled_error_title": "Something went wrong", "notifications_enabled_error_desc": "We couldn't enable notifications. Please try again later.", "prompt_desc": "Turn on notifications from <PERSON><PERSON><PERSON> to get important alerts on wallet activity and more.", "prompt_ok": "Turn on", "prompt_cancel": "Maybe later", "wc_connected_title": "Connected to {{title}}", "wc_signed_title": "Signed", "wc_sent_tx_title": "Sent transaction", "wc_connected_rejected_title": "You've rejected the connect request", "wc_signed_rejected_title": "You've rejected the sign request", "wc_signed_failed_title": "This sign request failed", "wc_sent_tx_rejected_title": "You've rejected the transaction request", "approved_tx_rejected_title": "You've rejected granting permission", "wc_description": "Please check the application", "wallet": "Wallet", "web3": "Web3", "staking_provider": "Staking Provider", "network_fee_not_available": "Network Fee not available", "empty": {"title": "Nothing to see here", "message": "This is where you can find notifications once there’s activity in your wallet. "}, "list": {"0": "All", "1": "Wallet", "2": "Annoucements"}, "copied_to_clipboard": "Copied to clipboard", "address_copied_to_clipboard": "Address copied to clipboard", "transaction_id_copied_to_clipboard": "Transaction ID copied to clipboard", "activation_card": {"title": "Turn on notifications", "description_1": "Stay in the loop on what's happening in your wallet with notifications.", "description_2": "To use this feature, we’ll generate an anonymous ID for your account. It’s used only for syncing your data in MetaMask and doesn't link to your activities or other identifiers, ensuring your privacy.", "learn_more": "Learn how we protect your privacy while using this feature.", "manage_preferences_1": "You can turn off notifications at any time in ", "manage_preferences_2": "Settings > Notifications.", "cancel": "Cancel", "cta": "Turn on"}}, "protect_your_wallet_modal": {"title": "Protect your wallet", "body_for_password": "Protect your wallet by setting a password and saving your Secret Recovery Phrase (required).", "body_for_seedphrase": "Now that value was added to your wallet, protect your wallet by setting a password and saving your Secret Recovery Phrase (required).", "button": "Protect wallet"}, "transaction_update_retry_modal": {"title": "Transaction Update Failed", "text": "Would you like to try again?", "cancel_button": "Cancel", "retry_button": "Retry"}, "payment_request": {"title": "Request", "search_top_picks": "Top picks", "search_assets": "Search assets", "search_results": "Search results", "search_no_tokens_found": "No tokens found", "your_tokens": "Your tokens", "enter_amount": "Enter amount", "choose_asset": "Choose an asset to request", "request_error": "Invalid request, please try again", "reset": "Reset", "next": "Next", "amount_placeholder": "0.00", "link_copied": "Link copied to clipboard", "send_link_title": "Send Link", "description_1": "Your request link is ready to send!", "description_2": "Send this link to a friend, and it will ask them to send", "copy_to_clipboard": "Copy to Clipboard", "qr_code": "QR Code", "send_link": "Send Link", "request_qr_code": "Payment Request QR Code", "balance": "Balance"}, "receive_request": {"title": "Receive", "share_title": "Share Address", "share_description": "Email or text your address", "qr_code_title": "QR Code", "qr_code_description": "Scannable image that can read your address", "request_title": "Request", "request_description": "Request assets from friends", "buy_title": "Buy", "buy_description": "Buy crypto with debit card or bank transfer", "public_address": "Public Address", "public_address_qr_code": "Public Address", "coming_soon": "Coming soon...", "request_payment": "Request Payment", "copy": "Copy", "scan_address": "Scan address to receive payment", "copy_address": "Copy address"}, "experimental_settings": {"wallet_connect_dapps": "WalletConnect Sessions", "wallet_connect_dapps_desc": "View the list of active WalletConnect sessions", "wallet_connect_dapps_cta": "View sessions", "network_not_supported": "Current network not supported", "select_provider": "Select your preferred provider", "switch_network": "Please switch to mainnet or sepolia"}, "walletconnect_sessions": {"no_active_sessions": "You have no active sessions", "end_session_title": "End Session", "end": "End", "cancel": "Cancel", "session_ended_title": "Session Ended", "session_ended_desc": "The selected session has been terminated", "session_already_exist": "This session is already connected.", "close_current_session": "Close the current session before starting a new one."}, "paymentRequest": {"title": "PAYMENT REQUEST", "title_complete": "PAYMENT COMPLETE", "confirm": "PAY", "cancel": "DECLINE", "is_requesting_you_to_pay": "is requesting you to pay", "total": "TOTAL:"}, "webview_error": {"title": "Something went wrong", "message": "We couldn't load that page", "return_home": "Return to home page"}, "offline_mode": {"title": "You're offline", "text": "Unable to connect to the blockchain host.", "try_again": "Try again", "learn_more": "Learn more"}, "walletconnect_return_modal": {"title": "You're all set!", "text": "You can now return to your browser"}, "account_bar": {"depositing_to": "Depositing to:"}, "fiat_on_ramp": {"buy_eth": "Buy ETH", "buy": "Buy {{ticker}}", "purchased_currency": "Purchased {{currency}}", "network_not_supported": "Current network not supported", "switch_network": "Please switch to Mainnet", "switch": "Switch", "purchases": "Purchases", "purchase_method": "Purchase Method", "amount_to_buy": "Amount to buy", "transak_webview_title": "Transak", "moonpay_webview_title": "MoonPay", "wyre_user_agreement": "Wyre User Agreement", "wyre_terms_of_service": "Wyre Terms of Service", "best_deal": "Best deal", "purchase_method_title": {"wyre_first_line": "0% fee when you use", "wyre_second_line": "Apple Pay.", "wyre_sub_header": "Valid until July 1st, 2020", "first_line": "How do you want to make", "second_line": "your purchase?"}, "buy_ticker": "Buy {{ticker}}", "buy_ticker_stablecoins": "Buy {{ticker}} and Stablecoins", "multiple_payment_methods": "Multiple Payment Methods", "debit_credit_bank_transfers_country": "Debit/credit and bank transfers based on country.", "debit_credit_bank_transfers_more_country": "Debit/credit, bank transfers, and more based on country.", "options_fees_vary": "100+ countries, fees and limits vary", "moonpay_options_fees_vary": "145+ countries, fees and limits vary", "some_states_excluded": "Some states excluded", "purchase_method_modal_close": "Close", "transak_cta": "Buy ETH with Transak", "transak_cta_ticker": "Buy {{ticker}} with Transak", "apple_pay": "Apple Pay", "via": "via", "fee": "fee", "Fee": "Fee", "limited_time": "limited time", "supported_countries": "Supported countries", "no_countries_result": "No supported countries match “{{searchString}}”", "wyre_loading_rates": " ", "fast_no_registration": "Fast - No registration required", "debit_credit_card_required": "Debit or Credit cards. Apple Cash is not supported.", "select_card_country": "Select the country where your card is registered (not where you are located).", "search_country": "Search for a country", "wyre_countries": "55+ countries, fees & limits vary", "wyre_fees_us": "2.9% + $0.30 + gas fees ($5 minimum fee)", "wyre_fees_us_fee": "Fee of 2.9% + $0.30 + gas ($5 minimum)", "wyre_fees_outside_us_fee": "Fee of 3.9% + $0.30 + gas ($5 minimum)", "wyre_estimated": "Estimated {{amount}} {{currency}}", "wyre_modal_text": "Paying with Apple Pay, powered by Wyre is supported in the United States 🇺🇸 except for CT, HI, NC, NH, NY, VA and VT.", "wyre_minimum_deposit": "Minimum deposit is {{amount}}", "wyre_maximum_deposit": "Maximum deposit is {{amount}}", "apple_pay_purchase": "{{currency}} Purchase", "apple_pay_provider_total_label": "{{provider}} (via MetaMask)", "buy_with": "Buy with", "plus_fee": "Plus a {{fee}} fee", "date": "Date", "from": "From", "to": "To", "status": "Status", "completed": "Completed", "pending": "Pending", "failed": "Failed", "cancelled": "Canceled", "amount": "Amount", "total_amount": "Total amount", "gas_education_carousel": {"step_1": {"title": "Before you purchase {{ticker}}, understand gas fees", "average_gas_fee": "Current average gas fee:", "subtitle_1": "If you plan to swap or transfer your {{ticker}}, purchase extra to cover gas fees.", "subtitle_2": "Gas fees for transactions are separate from the cost of purchasing {{ticker}}.", "subtitle_3": "MetaMask does not profit from gas fees."}, "step_2": {"title": "What are gas fees?", "subtitle_1": "Gas powers transactions on the Ethereum network. It’s a fee paid in {{ticker}} to the crypto miners who process your transactions.", "subtitle_2": "MetaMask does not profit from gas fees.", "learn_more": "Learn more about gas fees"}, "step_3": {"title": "How much do I need?", "subtitle_1": "Gas fees fluctuate based on network traffic and type of transaction.", "subtitle_2": "A complex transaction like “swapping” may cost 5x - 10x more than a “send” transaction.", "subtitle_3": "The best way to estimate gas fees is to", "subtitle_4": "try the transaction first", "subtitle_5": "and see how much gas costs.", "cta": "Continue to purchase {{ticker}}"}}}, "fiat_on_ramp_aggregator": {"buy": "buy", "sell": "sell", "orders": "Transfers", "All": "All", "Buy": "Buy", "Sell": "<PERSON>ll", "token_marketplace": "Token marketplace", "Purchased": "Purchased", "Sold": "Sold", "empty_orders_list": "No bank or card transfers yet.", "empty_buy_orders_list": "You don't have any buy orders", "empty_sell_orders_list": "You don't have any sell orders", "purchased_currency": "Purchased {{currency}}", "sold_currency": "Sold {{currency}}", "order_status_pending": "Pending", "order_status_processing": "Processing", "order_status_completed": "Completed", "order_status_failed": "Failed", "order_status_cancelled": "Cancelled", "network_switcher": {"title": "Unsupported {{rampType}} Network", "description": "To {{rampType}} crypto, you'll need to switch to a supported network", "no_networks_found": "No supported networks found"}, "onboarding": {"what_to_expect": "What to Expect", "quotes": "Our buy crypto feature aggregates quotes from integrated vendors, providing quotes from those sources to get crypto directly into your wallet with no waiting period.", "quotes_sell": "Now you can cash out directly in MetaMask! Get up-to-the-minute quotes from trusted providers while we guide you every step of the way.", "benefits": "Fewer gas fees to pay, and numerous networks, tokens, and payment methods supported", "benefits_sell": "Off-ramping from crypto to your local currency is now easier than ever.", "get_started": "Get started"}, "payment_method": {"payment_method": "Payment Method", "cash_destination": "Cash Destination", "instant": "Instant", "less_than": "Less than", "minute": "min", "minutes": "mins", "hour": "hour", "hours": "hours", "business_day": "business day", "business_days": "business days", "lowest_limit": "lowest buy limit", "medium_limit": "medium buy limit", "highest_limit": "highest buy limit", "lowest_sell_limit": "lowest sell limit", "medium_sell_limit": "medium sell limit", "highest_sell_limit": "highest sell limit", "continue_to_amount": "Continue to amount", "no_payment_methods_title": "No Payment Methods in {{regionName}}", "no_cash_destinations_title": "No Cash Destinations in {{regionName}}", "no_payment_methods_description": "There are currently no supported payment methods in your region. Please check by soon; we are frequently expanding support to new regions.\n\nIf you selected {{regionName}} by mistake, click the button below to reset your region.", "no_cash_destinations_description": "There are currently no supported cash destinations in your region. Please check by soon; we are frequently expanding support to new regions.\n\nIf you selected {{regionName}} by mistake, click the button below to reset your region.", "reset_region": "Reset Region"}, "continue": "Continue", "new_quotes_in": "New quotes in", "fetching_new_quotes": "Fetching new quotes...", "quotes_expire_in": "Quotes expire in", "get_new_quotes": "Get new quotes", "explore_more_options": "Explore more options", "one_more_option": "1 more option", "more_options": "{{count}} more options", "previously_used": "Previously used", "best_rate": "Best rate", "most_reliable": "Most reliable", "quotes_timeout": "Quotes timeout", "request_new_quotes": "Please request new quotes to get the latest best rate.", "terms_of_service": "Terms of Service", "amount_to_buy": "Amount to buy", "amount_to_sell": "Amount to sell", "want_to_buy": "You want to buy", "want_to_sell": "You want to sell", "current_balance": "Current balance", "amount": "Amount", "send_cash_to": "Send your cash to", "get_quotes": "Get quotes", "done": "Done", "fetching_quotes": "Fetching quotes", "select_a_quote": "Select a Quote", "recommended_quote": "Recommended quote", "select_a_cryptocurrency": "Select a cryptocurrency", "select_a_cryptocurrency_description": "Select from the list of tokens available.", "search_by_cryptocurrency": "Search by cryptocurrency", "search_by_currency": "Search for a currency", "update_payment_method": "Update payment method", "select_payment_method": "Select payment method", "select_cash_destination": "Select where to send your cash", "select_region_currency": "Select Region Currency", "no_tokens_match": "No tokens match “{{searchString}}”", "no_currency_match": "No currencies match “{{searchString}}”", "compare_rates": "Compare rates from these providers. Quotes are sorted by overall price.", "pay_with": "Pay with", "continue_with": "Continue with {{provider}}", "minimum": "Minimum deposit is", "maximum": "Maximum deposit is", "insufficient_balance": "This amount is higher than your balance", "insufficient_native_balance": "You don't have enough {{currency}} to cover the gas fees.", "enter_larger_amount": "Enter a larger amount to continue", "enter_smaller_amount": "Enter a smaller amount to continue", "enter_lower_gas_fees": "Enter a lower amount to pay for gas fees", "max": "MAX", "try_again": "Try again", "error": "Error", "something_went_wrong": "Oops, something went wrong", "report_this_issue": "Report this issue", "no_providers_available": "No providers available", "try_different_amount_to_buy_input": "Try choosing a different payment method or try to increase or reduce the amount you want to buy!", "try_different_amount_to_sell_input": "Try choosing a different cash out destination or try to increase or reduce the amount you want to sell!", "webview_received_error": "WebView received error status code: {{code}}", "no_tokens_available_title": "No Tokens Available", "no_tokens_available": "There are currently no tokens available to purchase on {{network}} with the selected payment method.", "no_sell_tokens_available": "There are currently no tokens available to sell on {{network}} with the selected cash destination.", "this_network": "this network", "change_payment_method": "Change payment method", "change_cash_destination": "Change cash destination", "try_different_region": "Try a different region", "return_home": "Return to Home Screen", "region": {"buy_crypto_tokens": "Buy Crypto Tokens", "sell_crypto_tokens": "<PERSON><PERSON><PERSON>", "title": "Select your Region", "description": "The payment methods and tokens available to you are determined by our third-party integrations and may vary depending on your region and support by our integrations.", "sell_description": "Cash destination options and tokens may vary depending on your region.", "search_by_country": "Search by country", "search_by_state": "Search by state", "no_region_results": "No region matches", "your_region": "Your Region", "select_region": "Select your region", "select_region_title": "Select your Region", "select_country_registered": "Select the country where your card is registered (regardless of where you are located).", "unsupported": "Region Not Supported", "unsupported_description": "We are working hard to expand coverage to your region as soon as we can. In the meantime, see our support article for other ways you may be able to {{rampType}} crypto.", "unsupported_link": "Visit Support Article", "popular_regions": "Popular Regions", "regions": "Regions"}, "order_details": {"details_main": "Order Details", "successful": "Order Successful!", "your": "Your", "available_in_account": "is now available in your account", "delayed_bank_transfer": "It make take a few days to see the cash in your bank account.", "crypto": "crypto", "failed": "Order Failed", "cancelled": "Order Canceled", "pending": "Order Pending", "failed_description": "Something went wrong, and {{provider}} was unable to complete your order. Please try again or with another provider.", "continue_order_description": "To continue your order, you'll need to select the button at the bottom of this page.", "the_provider": "the provider", "processing": "Processing Order", "processing_card_description": "Credit/Debit purchases typically take a few minutes", "processing_bank_description": "Bank transfers typically take a few business days", "details": "Order details", "via": "via", "purchase_amount": "Purchase Amount Total", "amount_received_total": "Amount Received Total", "etherscan": "View full details on", "start_new_order": "Start a new order", "continue_order": "Continue this order", "questions": "Questions?", "contact_support": "Contact Support", "support": "Support", "view_order_status": "View order status on {{provider}}", "id": "Order ID", "date_and_time": "Date and Time", "payment_method": "Payment Method", "destination": "Destination", "token_amount": "<PERSON><PERSON> Amount", "token_quantity_sold": "Token Quantity Sold", "exchange_rate": "Exchange Rate", "total_fees": "Total Fees", "amount": "Amount", "value": "Value", "a_block_explorer": "a block explorer"}, "send_transaction": {"sell_crypto": "Sell crypto", "send_description": "You will send your {{cryptocurrency}} to {{provider}}, who then sends your cash to {{paymentMethod}}", "next": "Next", "hold_to_send": "Hold to send", "send": "Send", "sent": "Sent!"}, "notifications": {"purchase_failed_title": "Purchase of {{currency}} has failed! Please try again, sorry for the inconvenience!", "purchase_failed_description": "Verify your payment method and card support", "purchase_cancelled_title": "Your purchase was cancelled", "purchase_cancelled_description": "Verify your payment method and card support", "purchase_completed_title": "Your purchase of {{amount}} {{currency}} was successful!", "purchase_completed_description": "Your {{currency}} is now available", "purchase_pending_title": "Processing your purchase of {{currency}}", "purchase_pending_description": "This should only take a few minutes...", "sale_failed_title": "Order failed", "sale_failed_description": "Your order couldn´t be completed.", "sale_cancelled_title": "Order cancelled", "sale_cancelled_description": "Your order couldn´t be completed.", "sale_completed_title": "Order completed", "sale_completed_description": "Your order was successful!.", "sale_pending_title": "{{currency}} sale processing", "sale_pending_description": "Your order is now being processed.", "no_date": "Unknown"}, "deposit_order_title": "{{currency}} Deposit"}, "swaps": {"title": "<PERSON><PERSON><PERSON>", "onboarding": {"get_the": "Get the", "best_price": "best price", "from_the": "from the", "top_liquidity": "top liquidity", "sources": "sources.", "find_the": "Find the", "best_swap": "best swap", "across": "across...", "want_to_learn_more": "Want to learn more?", "learn_more": "Learn more about MetaMask Swaps", "what_are": "What are token swaps?", "review_audits": "Review our official contracts audit", "start_swapping": "Start swapping"}, "feature_off_title": "Temporarily unavailable", "feature_off_body": "MetaMask Swaps is undergoing maintenance. Please check back later.", "wrong_network_title": "Swaps not available", "wrong_network_body": "You’re only able to swap tokens on the Ethereum Main Network.", "unallowed_asset_title": "Can’t swap this token", "unallowed_asset_body": "Some tokens with unique mechanics are currently not supported for swapping.", "convert_from": "Convert from", "convert_to": "Convert to", "verify": "Verify", "verified_on_sources": "Verified on {{sources} sources.", "verify_on": "Always verify the token address on", "verify_address_on": "Verify token address on", "added_manually": "This token has been added manually.", "verify_this_token_on": "Verify this token on", "only_verified_on": "{{symbol}} is only verified on {{occurrences}} source.", "block_explorer": "block explorer", "a_block_explorer": "a block explorer", "make_sure_trade": "and make sure this is the token you want to trade.", "token_verification": "Token verification", "token_multiple": "Multiple tokens can use the same name and symbol.", "token_check": "Check", "token_to_verify": "to verify this is the token you're looking for.", "continue": "Continue", "select_a_token": "Select a token", "search_token": "Enter token name or paste address", "no_tokens_result": "No tokens match “{{searchString}}”", "find_token_address": "Find token address", "cant_find_token": "Can’t find a token?", "manually_pasting": "Manually add a token by pasting its address.", "token_address_can_be_found": "Token contract addresses can be found on", "gathering_token_details": "Gathering token details...", "error_gathering_token_details": "Oops, there was an error gathering token details.", "Import": "Import", "invalid_token_contract_address": "Invalid token contract address.", "please_verify_on_explorer": "Please verify on", "add_warning": "Anyone can create a token, including creating fake versions of existing tokens that claim to represent projects.", "import_token": "Import token?", "contract": "Contract:", "available_to_swap": "{{asset}} available to swap.", "use_max": "Use max", "not_enough": "Not enough {{symbol}} to complete this swap", "max_slippage": "<PERSON> Slippage", "max_slippage_amount": "Max slippage {{slippage}}", "slippage_info": "If the rate changes between the time your order is placed and confirmed it’s called “slippage”. Your Swap will automatically cancel if slippage exceeds your “max slippage” setting.", "slippage_warning": "Make sure you know what you’re doing!", "allows_up_to_decimals": "{{symbol}} allows up to {{decimals}} decimals", "get_quotes": "Get quotes", "starting": "Starting...", "fetching_quotes": "Fetching quotes", "finalizing": "Finalizing...", "quote": "Quote", "of": "of", "checking": "Checking", "fetching_new_quotes": "Fetching new quotes...", "you_need": "You need", "more_to_complete": "more to complete this swap.", "more_gas_to_complete": "more for gas to complete this swap.", "token_marketplace": "Token marketplace", "market_price_unavailable_title": "Check your rate before proceeding", "market_price_unavailable": "Market price is unavailable and price impact is therefore unknown. Please verify that you are comfortable with the amount of tokens you will receive before swapping.", "price_difference": "Price difference of {{amount}}", "price_difference_title": "Price difference", "price_difference_body": "The difference in market prices can be affected by fees taken by intermediaries, size of market, size of trade, or market inefficiencies.", "price_impact_title": "Price impact", "price_impact_body": "Price impact is the difference between the current market price and the amount received during transaction execution. Price impact is a function of the size of your trade relative to the size of the liquidity pool.", "quotes_update_often": "Quotes update often", "quotes_update_often_text": "Quotes are updated often to reflect current market conditions.", "about_to_swap": "You are about to swap", "for": "for", "new_quotes_in": "New quotes in", "i_understand": "I understand", "quotes_expire_in": "Quotes expire in", "saving": "Saving", "n_quotes": "{{numberOfQuotes}} quotes", "view_details": "View details", "estimated_gas_fee": "Estimated gas fee", "gas_fee": "Gas fee", "included": "included", "max_gas_fee": "Max gas fee", "edit": "Edit", "quotes_include_fee": "Quotes include a {{fee}}% MetaMask fee", "quotes_include_gas_and_metamask_fee": "Quote includes gas and a {{fee}}% MetaMask fee", "tap_to_swap": "Tap to Swap", "swipe_to_swap": "Swipe to swap", "swipe_to": "Swipe to", "swap": "<PERSON><PERSON><PERSON>", "completed_swap": "Swap!", "metamask_swap_fee": "MetaMask Swap fee", "fee_text": {"fee_is_applied": "The fee of {{fee}} is automatically factored into this quote. You pay it in exchange for a license to use MetaMask's liquidity provider information aggregation software.", "fee_is_not_applied": "MetaMask does not include an additional fee in this quote."}, "enable": {"this_will": "This will", "enable_asset": "enable {{asset}}", "for_swapping": "for swapping", "edit_limit": "Edit limit"}, "quotes_overview": "Quotes overview", "quote_details": "Quote details", "receiving": "Receiving", "overall_value": "Overall value", "best": "Best", "rate": "Rate", "quote_details_max_slippage": "Max slippage", "source": "Source", "estimated_network_fees": "Estimated gas fees", "guaranteed_amount": "Guaranteed amount", "quote_source_dex": {"1": "This quote comes directly from a", "2": "decentralized exchange", "3": "protocol."}, "quote_source_rfq": {"1": "This quote comes from a", "2": "private market maker", "3": "who responds with a quote directly."}, "quote_source_agg": {"1": "This quote comes from an", "2": "aggregator", "3": "which compares prices and splits your order between different decentralized exchanges."}, "quote_source_cnt": {"1": "This quote comes directly from a", "2": "smart contract", "3": "that wraps and unwraps native tokens which can save on gas costs."}, "quotes_timeout": "Quotes timeout", "request_new_quotes": "Please request new quotes to get the latest best rate.", "quotes_not_available": "Quotes not available", "try_adjusting": "Try adjusting the amount and try again.", "error_fetching_quote": "Error fetching quote", "unexpected_error": "There has been an unexpected error, please request new quotes to get the latest best rate. (error: {{error}})", "get_new_quotes": "Get new quotes", "try_again": "Try again", "terms_of_service": "Terms of service", "transaction_label": {"swap": "Swap {{sourceToken}} to {{destinationToken}}", "approve": "Approve {{sourceToken}} for swaps: Up to {{upTo}}"}, "notification_label": {"swap_pending": "Pending Swap ({{sourceToken}} to {{destinationToken}})", "swap_confirmed": "Swap complete ({{sourceToken}} to {{destinationToken}})", "approve_pending": "Approving {{sourceToken}} for swaps", "approve_confirmed": "{{sourceToken}} approved for swaps"}, "medium_selected_warning": "Swaps are time sensitive. “Medium” is not recommended.", "high_recommendation": "Swaps are typically time sensitive. “High” will help avoid potential losses due to changes in market conditions.", "recommended": "Recommended", "recommended_gas": "Recommended gas fee", "gas_included_tooltip_explanation": "This quote incorporates gas fees by adjusting the token amount sent or received. You may receive ETH in a separate transaction on your activity list.", "gas_included_tooltip_explanation_link_text": "Learn more about gas fees", "gas_education_title": "Estimated gas fees", "gas_education_1": "Gas fees are paid to crypto miners who process transactions on the ", "gas_education_2_ethereum": "Ethereum network.", "gas_education_2": "network.", "gas_education_3": "MetaMask does not profit from gas fees.", "gas_education_4": "The", "gas_education_5": "“Estimated gas fee”", "gas_education_6": "is what we expect the actual fee to be. The exact amount depends on network conditions.", "gas_education_7": "“Max gas fee”", "gas_education_8": "is the most you’ll spend. When the network is volatile this can be a large amount.", "gas_education_learn_more": "Learn more about gas fees"}, "protect_wallet_modal": {"title": "Protect your wallet", "top_button": "Protect wallet", "bottom_button": "Remind me later", "text": "Don’t risk losing your funds. Protect your wallet by saving your Secret Recovery Phrase in a place you trust.", "text_bold": "It’s the only way to recover your wallet if you get locked out of the app or get a new device.", "action": "Learn more"}, "deeplink": {"invalid": "Invalid deeplink", "not_supported": "Deeplink not supported"}, "error_screen": {"title": "An error occurred", "subtitle": "Your information can't be shown. Don’t worry, your wallet and funds are safe.", "try_again_button": "Try again", "submit_ticket_1": "Please report this issue so we can fix it:", "submit_ticket_2": "Take a screenshot of this screen.", "submit_ticket_3": "Copy", "submit_ticket_4": "the error message to clipboard.", "submit_ticket_5": "Submit a ticket", "submit_ticket_6": "here.", "submit_ticket_7": "Please include the error message and the screenshot.", "submit_ticket_8": "Send us a bug report", "submit_ticket_9": "Please include details about what happened.", "bug_report_prompt_title": "Tell us what happened", "bug_report_prompt_description": "Add details so we can figure out what went wrong.", "bug_report_thanks": "Thanks! We’ll take a look soon.", "save_seedphrase_1": "If you keep getting this error,", "save_seedphrase_2": "Save your Secret Recovery Phrase", "save_seedphrase_3": "and re-install the app. Remember: without your Secret Recovery Phrase, you can't restore your wallet.", "copied_clipboard": "Copied to clipboard", "ok": "OK", "cancel": "Cancel", "send": "Send", "submit": "Submit", "modal_title": "Describe what happened", "modal_placeholder": "Sharing details like how we can reproduce the bug will help us fix the problem.", "error_message": "Error message:", "copy": "Copy", "describe": "Describe what happened", "try_again": "Try again", "contact_support": "Contact support"}, "whats_new": {"title": "What's New", "remove_gns_new_ui_update": {"title": "New UI update", "introduction": "We've made updates to improve the app experience.", "descriptions": {"description_1": "Network dropdown moved to your assets", "description_2": "Swap and Bridge in one simple flow", "description_3": "Streamlined Send experience", "description_4": "A fresh account view"}, "more_information": "Now you can focus on your tokens and activity, not the networks behind them.", "got_it": "Got it"}}, "invalid_network": {"title": "The chain ID for custom network \n %{network} \n has to be re-entered.", "message": "To protect you from malicious or faulty network providers, chain IDs are now required for all custom networks.", "hint": "You can find the chain IDs of most popular networks on ", "edit_network_button": "Edit network", "cancel": "Cancel"}, "switch_custom_network": {"title_existing_network": "This site would like to switch the network", "title_new_network": "New network added", "switch_warning": "This will switch the selected network within MetaMask to a previously added network:", "add_network_and_give_dapp_permission_warning": "You're adding this network to MetaMask and giving {{dapp_origin}} permission to use it.", "update_network_and_give_dapp_permission_warning": "You're updating this network in MetaMask and giving {{dapp_origin}} permission to use it.", "request_update_network_url": "{{dapp_origin}} is requesting to update your default network URL. You can edit defaults and network information any time.", "available": "is now available in the network selector.", "cancel": "Cancel", "switch": "Switch Network"}, "add_custom_network": {"title": "Allow this site to add a network?", "warning": "This allows this network to be used within MetaMask.", "warning_subtext_1": "MetaMask does not verify custom networks or their security.", "warning_subtext_2": "Learn about", "warning_subtext_3": "scams and network security risks", "display_name": "Display name", "chain_id": "Chain ID", "network_url": "Network URL", "currency_symbol": "Currency symbol", "block_explorer_url": "Block explorer URL", "details_title": "Network details", "cancel": "Cancel", "approve": "Approve", "unrecognized_chain": "This custom network is not recognized", "invalid_chain": "The %{rpcUrl} for this Chain ID does not match the chainid.network listing", "alert_recommend": "We recommend you", "alert_verify": "verify the chain ID", "warning_subtext_new": {"1": "MetaMask doesn’t verify custom networks, so only approve networks you trust.", "2": "Learn more about network security risks and scams."}, "invalid_rpc_url": "This network URL doesn't match a known provider for this chain ID.", "invalid_chain_token_decimals": "It looks like this network's decimal doesn't match its chain ID.", "unrecognized_chain_name": "It looks like this network's display name doesn't match its chain ID.", "unrecognized_chain_ticker": "It looks like this network's symbol doesn't match this chain ID.", "unrecognized_chain_id": "We don't recognize this network. Be sure the chain ID is correct before you continue."}, "media_player": {"loading": "Loading...", "not_found": "Media not found"}, "edit_gas_fee_eip1559": {"advanced_options": "Advanced options", "gas_limit": "Gas limit", "max_priority_fee": "Max priority fee", "max_fee": "Max fee", "estimate": "Estimate", "recommended_gas_fee": "Recommended gas fee", "swaps_warning": "Swaps are very time sensitive. “High” will help avoid potential losses due to changes in market conditions.", "priority_fee_at_least_0_error": "Priority fee must be at least 1 GWEI", "learn_more": {"title": "How should I choose?", "intro": "Selecting the right gas fee depends on the type of transaction and how important it is to you.", "aggressive_label": "Aggressive", "aggressive_text": "High probability, even in volatile markets. Use Aggressive to cover surges in network traffic due to things like popular NFT drops.", "market_label": "Market", "market_text": "Use Market for fast processing at current market price.", "low_label": "Low", "low_text": "Use Low to wait for a cheaper price. Time estimates are much less accurate as prices are somewhat unpredictable.", "link": "Learn more about customizing gas."}, "save": "Save", "submit": "Submit", "max_priority_fee_low": "Max Priority Fee is low for current network conditions", "max_priority_fee_high": "Max Priority Fee is higher than necessary", "max_priority_fee_speed_up_low": "Max Priority Fee must be at least {{speed_up_floor_value}} GWEI (10% higher than initial transaction)", "max_priority_fee_cancel_low": "Max Priority Fee must be at least {{cancel_value}} GWEI (50% higher than initial transaction)", "max_fee_low": "Max Fee is low for current network conditions", "max_fee_high": "Max Fee is higher than necessary", "max_fee_speed_up_low": "Max Fee must be at least {{speed_up_floor_value}} GWEI (10% higher than initial transaction)", "max_fee_cancel_low": "Max Fee must be at least {{cancel_value}} GWEI (50% higher than initial transaction)", "learn_more_gas_limit": "Gas limit is the maximum units of gas you are willing to use. Units of gas are a multiplier to “Max priority fee” and “Max fee”. ", "learn_more_max_priority_fee": "Max priority fee (aka “miner tip”) goes directly to miners and incentivizes them to prioritize your transaction. You’ll most often pay your max setting. ", "learn_more_max_fee": "The max fee is the most you’ll pay (base fee + priority fee). ", "learn_more_new_gas_fee": "We have updated the gas fee based on current network conditions and have increased it by at least 10% (required by the network). ", "learn_more_cancel_gas_fee": "We have updated the gas fee based on current network conditions and have increased it by at least 50% (required by the network). ", "low": "Low", "medium": "Medium", "high": "High", "market": "Market", "aggressive": "Aggressive", "low_fee_warning": "Take note of your processing time. Future transactions will queue after this one.", "edit_priority": "Edit priority", "speed_up_transaction": "Speed up transaction", "cancel_transaction": "Cancel transaction", "new_gas_fee": "New gas fee", "edit_suggested_gas_fee": "Edit suggested gas fee", "gas_price": "Gas price", "learn_more_gas_limit_legacy": "Gas limit is the maximum units of gas you are willing to use. Units of gas are a multiplier to “Gas price”.", "learn_more_gas_price": "This network requires a “Gas price” field when submitting a transaction. Gas price is the maximum amount you are willing to pay per unit of gas.", "gas_price_low": "Gas price is low for current network conditions", "gas_price_high": "Gas price is higher than necessary"}, "transaction_review_eip1559": {"estimated_gas_fee": "Estimated gas fee", "network_fee": "Network fee", "max_fee": "Max fee", "total": "Total", "max_amount": "Max amount", "estimated_gas_fee_tooltip": "What are gas fees?", "estimated_gas_fee_tooltip_text_1": "Gas fees are paid to crypto miners who process transactions on the", "estimated_gas_fee_tooltip_text_2": " Ethereum", "estimated_gas_fee_tooltip_text_3": " network.", "estimated_gas_fee_tooltip_text_4": "MetaMask does not profit from gas fees.", "estimated_gas_fee_tooltip_text_5": "Gas fees are set by the network and fluctuate based on network traffic and transaction complexity.", "learn_more": "Learn more about gas fees", "legacy_gas_suggestion_tooltip": "This gas fee suggestion is using legacy gas estimation which may be inaccurate."}, "times_eip1559": {"unknown": "Unknown processing time", "maybe": "Maybe in", "likely": "Likely in <", "likely_in": "Likely in ", "very_likely": "Very likely in <", "at_least": "At least", "less_than": "Less than", "warning_very_likely": "This gas fee is much higher than the other options available.", "warning_very_likely_title": "Gas fees too high", "warning_unknown": "Your max fee or max priority fee may be low for current market conditions. We don’t know when (or if) your transaction will be processed.", "warning_low": "This lowers your maximum fee but if network traffic increases your transaction may be delayed or fail.", "warning_low_title": "Low priority", "warning_unknown_title": "Unknown processing time"}, "review_prompt": {"high_fees": "Why are fees so high?", "missing_tokens": "I'm missing tokens...", "swap_issues": "I'm unable to swap...", "mobile_sentiment": "How do you feel about MetaMask mobile?", "sentiment_good_face": "😁", "sentiment_bad_face": "☹️", "sentiment_good": "Love it!", "sentiment_bad": "Not great...", "help_title": "Oh no! Can we help?", "help_description_1": "We're here to help! Check out the FAQs below or ", "help_description_2": "contact support", "help_description_3": " for help!"}, "nickname": {"add_nickname": "Add nickname", "edit_nickname": "Edit nickname", "save_nickname": "Confirm", "address": "Address", "name": "Nickname", "name_placeholder": "Add a nickname to this address", "contract": "Contract", "nickname": "nickname"}, "network_information": {"things_to_keep_in_mind": "Things to keep in mind", "testnet_network": "{{type}} Testnet", "first_description": "The native token on this network is {{ticker}}. It is the token used for gas fees.", "second_description": "If you attempt to send assets directly from one network to another, this may result in permanent asset loss. Make sure to use a bridge.", "third_description": "Your tokens may not automatically show up in your wallet.", "private_network": "This network is unknown and may use a special token for gas fees.", "unknown_network": "Unknown network", "switched_network": "You have switched to", "learn_more": "Learn more", "add_token": "Click here to manually add the tokens", "add_token_manually": "Add them manually.", "got_it": "Got it", "error_title": "Oops! Something went wrong.", "error_message": "There was an error loading the network information. Please try again later.", "private_network_third_description": "Please enter a name for this network to make it easy to identify.", "learn_more_url": "https://support.metamask.io/networks-and-sidechains/managing-networks/user-guide-custom-networks-and-sidechains/", "enable_token_detection": "Enable auto token detection", "token_detection_mainnet_title": "Token detection is enabled so tokens will automatically show up in your wallet.", "token_detection_mainnet_link": "You can also add tokens manually.", "or": "or", "non_evm_first_description": "The native asset on this network is {{ticker}}. It is used for transaction fees.", "non_evm_second_description": "You will lose your assets if you try to send them from or to another network."}, "download_files": {"error": "Failed to download attachment.", "unknownError": "Unknown error occurred"}, "remember_me": {"enable_remember_me": "Turn on Remember me", "enable_remember_me_description": "When Remember me is on, anyone with access to your phone can access your MetaMask account."}, "turn_off_remember_me": {"title": "Enter your password to turn off Remember me", "placeholder": "Password", "description": "If you turn this option off, you'll need your password to unlock MetaMask from now on.", "action": "Turn off Remember me"}, "dapp_connect": {"warning": "In order to use this feature, please update the app to the newest version"}, "confirmation_modal": {"cancel_cta": "Cancel", "confirm_cta": "Confirm"}, "automatic_security_checks": {"title": "Automatic security checks", "description": "Automatically checking for updates may expose your IP address to GitHub servers. This only indicates that your IP address is using MetaMask. No other information or account addresses are exposed."}, "terms_of_use_modal": {"title": "Review our Terms of Use", "terms_of_use_check_description": "I agree to the Terms of Use, which apply to my use of MetaMask and all of its features", "accept_cta": "Accept", "accept_helper_description": "Please scroll to read all sections", "agree_cta": "Agree"}, "update_needed": {"title": "Get the newest features", "description": "We’ve made your wallet safer, smoother, and added some new features. Update now to stay protected and use our latest improvements.", "primary_action": "Update to latest version"}, "enable_automatic_security_check_modal": {"title": "Automatically check for security updates?", "description": "Automatically checking for updates may expose your IP address to GitHub servers. This only indicates that your IP address is using MetaMask. No other information or account addresses are exposed.", "primary_action": "Enable automatic security checks", "secondary_action": "No thanks"}, "contract_allowance": {"custom_spend_cap": {"max": "Max", "edit": "Edit", "title": "Spending cap", "use_site_suggestion": "Use site suggestion", "from_your_balance": "from your current balance.", "default_error_message": "Only enter a number that you're comfortable with the third party spending now or in the future. You can always increase the spending cap later.", "this_contract_allows": "This allows the third party to spend", "amount_greater_than_balance": "This allows the third party to spend all your token balance until it reaches the cap or you revoke the spending cap. If this is not intended, consider setting a lower spending cap.", "info_modal_description_default": "The third party could spend your entire token balance without further notice or consent. Protect yourself by setting a lower spending cap.", "set_spend_cap": "Set a spending cap", "be_careful": "Be careful", "error_enter_number": "Error: Enter only numbers", "enter_number": "Enter a number here", "learn_more": "Learn more"}, "token_allowance": {"verify_third_party_details": "Verify third party details", "protect_from_scams": "To protect yourself against scammers, take a moment to verify third party details.", "learn_to_verify": "Learn how to verify third party details", "spending_cap": "spending cap", "access": "access", "nft_contract": "NFT contract", "token_contract": "Token contract", "third_party_requesting_text": "Third party requesting {{action}}", "third party": "third party", "address": "Address"}}, "restore_wallet": {"restore_needed_title": "<PERSON><PERSON> needed", "restore_needed_description": "Something has gone wrong but don’t worry! Let’s try to restore your wallet.", "restore_needed_action": "Restore wallet"}, "wallet_restored": {"wallet_restored_title": "Your wallet is ready!", "wallet_restored_action": "Continue to wallet", "wallet_restored_description_part_one": "You may need to add some of your assets, networks, and settings again manually.", "wallet_restored_description_part_two": "Take a moment to", "wallet_restored_description_link": "back up your Secret Recovery Phrase", "wallet_restored_description_part_three": "in case you need to restore your wallet again in the future."}, "new_wallet_needed": {"new_wallet_needed_title": "New wallet needed", "new_wallet_needed_create_new_wallet_action": "Create a new wallet", "new_wallet_needed_create_try_again_action": "Try recovering wallet", "new_wallet_needed_description_part_one": "Something's wrong with your wallet, and you'll need to create a new one. Because your accounts are on the blockchain, they're still safe. Only the preferences, saved networks, account names, and related data saved on your device are gone.", "new_wallet_needed_description_part_two": "To import your accounts to a new wallet, you'll need your Secret Recovery Phrase. If you don't have your Secret Recovery Phrase, you won't be able to import your accounts.", "new_wallet_needed_description_part_three": "To keep this from happening again, be sure to always keep your MetaMask app and OS updated to the latest version."}, "srp_security_quiz": {"title": "Security quiz", "introduction": "To reveal your Secret Recovery Phrase, you need to correctly answer two questions", "get_started": "Get started", "learn_more": "Learn more", "try_again": "Try again", "continue": "Continue", "of": "of", "question_one": {"question": "If you lose your Secret Recovery Phrase, MetaMask...", "right_answer": "Can’t help you", "wrong_answer": "Can get it back for you", "right_answer_title": "Right! No one can help get your Secret Recovery Phrase back", "right_answer_description": "Write it down, engrave it on metal, or keep it in multiple secret spots so you never lose it. If you lose it, it’s gone forever.", "wrong_answer_title": "Wrong! No one can help get your Secret Recovery Phrase back", "wrong_answer_description": "If you lose your Secret Recovery Phrase, it’s gone forever. No one can help you get it back, no matter what they might say."}, "question_two": {"question": "If anyone, even a support agent, asks for your Secret Recovery Phrase...", "right_answer": "You’re being scammed", "right_answer_title": "Correct! Sharing your Secret Recovery Phrase is never a good idea", "right_answer_description": "Anyone claiming to need your Secret Recovery Phrase is lying to you. If you share it with them, they will steal your assets.", "wrong_answer": "You should give it to them", "wrong_answer_title": "Nope! Never share your Secret Recovery Phrase with anyone, ever", "wrong_answer_description": "Anyone claiming to need your Secret Recovery Phrase is lying to you. If you share it with them, they will steal your assets."}}, "ledger": {"open_settings": "Open settings", "view_settings": "View settings", "bluetooth_off": "Bluetooth is off", "bluetooth_off_message": "Please turn on Bluetooth for your device", "bluetooth_access_blocked": "<PERSON>ger needs Bluetooth access to pair with your mobile device.", "bluetooth_access_blocked_message": "If you want to pair your Ledger device using Bluetooth, enable it in Settings & try again.", "location_access_blocked": "MetaMask needs location access permission to pair with your ledger.", "location_access_blocked_message": "If you want to pair your Ledger device using Bluetooth, you’ll need to enable location access in Settings & try again.", "nearbyDevices_access_blocked": "MetaMask needs nearby devices permission to pair with your <PERSON><PERSON>.", "nearbyDevices_access_blocked_message": "If you want to pair your Ledger device using Bluetooth, you’ll need to enable nearby devices access in Settings & try again.", "bluetooth_scanning_error": "Error while scanning the device", "bluetooth_scanning_error_message": "Please make sure your device is unlocked and the Ethereum app is running", "bluetooth_connection_failed": "Bluetooth connection failed", "bluetooth_connection_failed_message": "Please make sure your Ledger is unlocked and your Bluetooth is enabled", "ethereum_app_open": "Ledger requires confirmation", "ethereum_app_open_message": "Please confirm on your device to open Ethereum app. Press OK once done.", "ethereum_app_unconfirmed_error": "You have denied the request to open the Ethereum app.", "failed_to_open_eth_app": "Failed to open the Ethereum app.", "ethereum_app_open_error": "Please install/accept Ethereum app on your Ledger device.", "running_app_close": "Failed to close the running app.", "running_app_close_error": "Failed to close the running app on your Ledger device.", "ethereum_app_not_installed": "Ethereum app not installed.", "ethereum_app_not_installed_error": "Please install the Ethereum app on your Ledger device.", "ledger_is_locked": "<PERSON>ger is locked", "unlock_ledger_message": "Please unlock your Ledger device", "cannot_get_account": "Cannot get account", "connect_ledger": "Connect Ledger", "looking_for_device": "Looking for device", "ledger_reminder_message": "Please make sure your Ledger device is:", "ledger_reminder_message_step_one": "1. Unlock your Ledger device", "ledger_reminder_message_step_two": "2. Install and open the Ethereum app", "ledger_reminder_message_step_three": "3. Enable Bluetooth", "ledger_reminder_message_step_four": "4. Location is enabled with use precise location on", "ledger_reminder_message_step_four_Androidv12plus": "4. Nearby devices is enabled", "ledger_reminder_message_step_five": "5. Do not disturb must be turned off", "blind_signing_message": "6. Enable \"blind signing\" on your Ledger device.", "available_devices": "Available devices", "retry": "Retry", "continue": "Continue", "confirm_transaction_on_ledger": "Confirm transaction on your Ledger", "bluetooth_enabled_message": "Make sure Bluetooth is enabled", "device_unlocked_message": "Device is unlocked", "ledger_disconnected": "Your device got disconnected", "ledger_disconnected_error": "The connection to your device has been lost. Please try again.", "unknown_error": "Unexpected error occurred.", "unknown_error_message": "An unexpected error occurred. Please try again.", "error_occured": "An error occurred", "how_to_install_eth_app": "How to install the Ethereum app on a Ledger device", "ledger_account_count": "You are using 1 account from your Ledger with MetaMask Mobile.", "open_eth_app": "Please open the Ethereum app", "open_eth_app_message_one": "We’ve detected you have the Ethereum app installed but it’s not open, please ", "open_eth_app_message_two": "press the two buttons on the device to accept the prompt to open the Ethereum app to continue.", "toast_bluetooth_connection_error_title": "Oops, something went wrong :/", "toast_bluetooth_connection_error_subtitle": "<PERSON><PERSON> has not been connected", "try_again": "Try again", "forget_device": "<PERSON>", "sign_with_ledger": "Sign with <PERSON><PERSON>", "ledger_pending_confirmation": "Ledger is busy", "ledger_pending_confirmation_error": "There is a pending action on your <PERSON><PERSON>. Please clear the action first then retry.", "not_supported": "Operation not supported", "not_supported_error": "Only version 4 of typed data signing is supported.", "error_during_connection": "An unknown error occurred", "error_during_connection_message": "There's been a slight problem connecting your Ledger device, tap 'Retry' below to give this another go. Sometimes this occurs due to the ETH app on your Ledger device being open at the start of the pairing process with MetaMask Mobile.", "how_to_install_eth_webview_title": "How to install the Ethereum App", "nonce_too_low": "Nonce too low", "nonce_too_low_error": "The set nonce is too low", "select_accounts": "Select an account", "select_hd_path": "Select HD Path", "select_hd_path_description": "If you don't see the accounts you expect, try switching the HD path or current selected network.", "ledger_live_path": "Ledger Live", "ledger_legacy_path": "Legacy(MEW/MyCrypto)", "ledger_bip44_path": "BIP44(e.g. <PERSON>, <PERSON><PERSON><PERSON>)", "ledger_legacy_label": " (legacy)", "blind_sign_error": "Blind signing error", "blind_sign_error_message": "Blind signing is not enabled on your Ledger device. Please enable it in the settings.", "user_reject_transaction": "User rejected the transaction", "user_reject_transaction_message": "The user has rejected the transaction on the Ledger device.", "multiple_devices_error_message": "Multiple devices aren’t supported yet. To add a new Ledger device, you’ll need to remove an older one.", "hd_path_error": "HD Path is invalid: {{path}}", "unspecified_error_during_connect": "Unspecified error when connect Ledger Hardware,", "account_name_existed": "Account {{accountName}} already exists"}, "account_actions": {"edit_name": "Edit account name", "add_account_or_hardware_wallet": "Add account or hardware wallet", "connect_hardware_wallet": "Connect an account", "import_wallet_or_account": "Import a wallet or account", "add_account": "Create a new account", "add_multichain_account": "Create a new {{networkName}} account", "create_an_account": "Create a new account", "add_new_account": "Ethereum account", "create_new_wallet": "Create a new wallet", "import_wallet": "Import a wallet", "import_srp": "Secret Recovery Phrase", "add_hardware_wallet": "Hardware wallet", "import_account": "Private key", "add_bitcoin_account": "Bitcoin account", "add_solana_account": "Solana account", "switch_to_smart_account": "Switch to Smart account", "rename_account": "Rename account", "addresses": "Addresses", "headers": {"bitcoin": "Bitcoin", "solana": "Solana"}}, "show_nft": {"show_nft_title": "Show NFT", "show_nft_content_1": "We use third-party services to show images of your NFTs stored on IPFS, display information related to ENS addresses entered in your browser's address bar, and fetch icons for different tokens. Your IP address may be exposed to these services when you’re using them.", "show_nft_content_2": "Selecting", "show_nft_content_3": "Confirm", "show_nft_content_4": "turns on IPFS resolution. You can turn it off in", "show_nft_content_5": "Settings > Security and privacy", "show_nft_content_6": "at any time."}, "show_display_nft_media": {"show_display_nft_media_title": "Display NFT media", "show_display_nft_media_content_1": "To see an NFT, you need turn on", "show_display_nft_media_content_2": "Display NFT media.", "show_display_nft_media_content_3": "Displaying NFT media and data exposes your IP address to OpenSea or other third parties. NFT autodetection relies on this feature, and won't be available when this is turned off.", "show_display_nft_media_content_4": "You can turn off Display NFT media in", "show_display_nft_media_content_5": "Settings > Security and privacy."}, "ipfs_gateway_banner": {"ipfs_gateway_banner_title": "IPFS gateway", "ipfs_gateway_banner_content1": "This is an IPFS website. to see this site, you need to turn on", "ipfs_gateway_banner_content2": "IPFS gateway", "ipfs_gateway_banner_content3": "in", "ipfs_gateway_banner_content4": "Settings."}, "ipfs_gateway": {"ipfs_gateway_title": "IPFS gateway", "ipfs_gateway_content1": "We use third-party services to show images of your NFTs stored on IPFS, display information related to ENS addresses entered in your browser's address bar, and fetch icons for different tokens. Your IP address may be exposed to these services when you’re using them.", "ipfs_gateway_content2": "You can turn off IPFS resolution in", "ipfs_gateway_content3": "Settings > Security and privacy", "ipfs_gateway_content4": "at any time"}, "install_snap": {"title": "Connection request", "description": "{{origin}} wants to use {{snap}}.", "permissions_request_title": "Permissions request", "permissions_request_description": "{{origin}} wants to install {{snap}}, which is requesting the following permissions.", "approve_permissions": "Approve", "installed": "Installed", "install_successful": "{{snap}} was successfully installed.", "okay_action": "OK", "error_title": "Install failed", "error_description": "Installation of {{snap}} failed."}, "earn": {"empty_state_cta": {"heading": "Lend {{tokenSymbol}} and earn", "body": "Lend your {{tokenSymbol}} with {{protocol}} and earn", "annually": "annually.", "learn_more": "Learn more.", "earn": "<PERSON><PERSON><PERSON>"}, "service_interruption_banner": {"maintenance_message": "We're down for maintenance. We'll be back online soon!"}, "deposit": "<PERSON><PERSON><PERSON><PERSON>", "approve": "Approve", "approval": "Approval", "confirm": "Confirm", "cancel": "Cancel", "transaction_submitted": "Transaction Submitted", "every_minute": "Every minute", "immediate": "Immediate", "apr": "APR", "protocol": "Protocol", "receive_tooltip": "receive tooltip", "button": "button", "receive": "Receive", "tooltip_content": {"apr": {"part_one": "Expected yearly increase of the value of your deposit, based on the reward rate over the past week.", "part_two": "Note: APR changes over time."}, "protocol": "A lending protocol is a smart contract that lets you lend tokens to earn rewards. It also lets users borrow tokens by putting up other tokens as collateral, against a fee which is paid to lenders.", "reward_frequency": "Frequency at which your rewards are accounted for.", "withdrawal_time": "The time it takes to withdraw your token from the protocol and get it back in your wallet", "receive": "This token is used to track your assets and rewards. Do not transfer or trade them, or you won’t be able to withdraw your assets.", "health_factor": {"your_health_factor_measures_liquidation_risk": "Your Health Factor measures liquidation risk", "above_two_dot_zero": "Above 2.0", "safe_position": "Safe position", "between_one_dot_five_and_2_dot_zero": "Between 1.5-2.0", "medium_liquidation_risk": "Medium Liquidation risk", "below_one_dot_five": "Below 1.5", "higher_liquidation_risk": "Higher liquidation risk"}, "lending_risk_aware_withdrawal_tooltip": {"why_cant_i_withdraw_full_balance": "Why can't I withdraw my full balance?", "your_withdrawal_amount_may_be_limited_by": "Your withdrawal amount may be limited by", "pool_liquidity": "Pool Liquidity", "not_enough_funds_available_in_the_lending_pool_right_now": "Not enough funds available in the lending pool right now.", "existing_borrow_positions": "Existing Borrow Positions", "withdrawing_could_put_your_existing_loans_at_risk_of_liquidation": "Withdrawing could put your existing loan positions at risk of liquidation."}}, "withdraw": "Withdraw", "deposit_more": "Deposit more", "earning": "Earning", "withdrawal_time": "<PERSON><PERSON><PERSON> time", "withdrawing_to": "Withdrawing to", "network": "Network", "health_factor": "Health factor", "liquidation_risk": "Liquidation risk", "insufficient_pool_liquidity": "Insufficient Pool Liquidity", "available_to_withdraw": "available to withdraw", "unknown": "unknown", "how_it_works": "How it works", "market_historic_apr_modal": {"earn_rewards_on_your_token": "Earn rewards on your {{tokenSymbol}}", "lend_and_earn_daily_rewards": "Lend your {{tokenSymbol}} with {{protocol}} and earn daily rewards. Rewards grow over time and APR varies.", "withdraw_whenever_you_want": "Withdraw whenever you want", "get_asset_back_in_your_wallet_instantly": "Get {{tokenSymbol}} back in your wallet instantly."}, "amount_exceeds_safe_withdrawal_limit": "Amount exceeds safe withdrawal limit", "view_earnings_history": {"lending": "View position history", "staking": "View earnings history"}, "earnings_history_list_title": {"lending": "Position history", "staking": "Payout history"}, "allowance_reset": "Allowance Reset"}, "stake": {"stake": "Stake", "earn": "<PERSON><PERSON><PERSON>", "stake_eth": "Stake ETH", "unstake_eth": "Unstake ETH", "staked_balance": "Staked balance", "staked_ethereum": "Staked Ethereum", "unstake": "Unstake", "stake_more": "Stake more", "claim": "<PERSON><PERSON><PERSON>", "your_earnings": "Your earnings", "annual_rate": "Annual rate", "lifetime_rewards": "Lifetime rewards", "estimated_annual_earnings": "Estimated annual earnings", "accessibility_labels": {"stake_annual_rate_tooltip": "Annual rate tooltip"}, "estimated_annual_rewards": "Estimated annual rewards", "estimated_annual_reward": "Est. annual reward", "reward_frequency": "Reward frequency", "reward_frequency_tooltip": "Your staked balance updates every {{frequency}} to account for new rewards.", "withdrawal_time": "<PERSON><PERSON><PERSON> time", "metamask_pool": "MetaMask Pool", "enter_amount": "Enter amount", "review": "Review", "not_enough_eth": "Not enough ETH", "not_enough_token": "Not enough {{ticker}}", "balance": "Balance", "stake_eth_and_earn": "Stake ETH and earn", "how_it_works": "How it works", "stake_any_amount_of_eth": "Stake any amount of ETH.", "no_minimum_required": "No minimum required.", "earn_eth_rewards": "Earn ETH rewards.", "earn_eth_rewards_description": "Start earning as soon as you stake. Rewards compound automatically.", "flexible_unstaking": "Flexible unstaking.", "flexible_unstaking_description": "Unstake anytime. Typically takes less than 3 days, but can take up to 11 days to process.", "disclaimer": "Staking does not guarantee rewards, and involves risks including a loss of funds.", "learn_more": "Learn more", "got_it": "Got it", "your_balance": "Your balance", "stake_your_eth_cta": {"base": "Stake your ETH with MetaMask Pool and earn", "annually": "annually.", "learn_more_with_period": "Learn more."}, "day": {"zero": "", "one": "day", "other": "days"}, "hour": {"zero": "", "one": "hour", "other": "hours"}, "minute": {"zero": "", "one": "minute", "other": "minutes"}, "banner_text": {"has_claimable_eth": "You can claim {{amountEth}} ETH. Once claimed, you'll get ETH back in your wallet.", "unstaking_in_progress": {"base": "Unstaking {{amountEth}} ETH in progress. Come back in", "and": "and", "to_claim_it": "to claim it.", "default": "Unstaking {{amountEth}} ETH in progress. Come back in a few days to claim it."}, "geo_blocked": "You’re located in a region where unstaking or staking isn’t permitted.", "approximately": "approximately"}, "unstake_input_banner_description": "On average, it takes less than 3 days for the unstaked ETH to be claimable, but can take up to 11 days.", "max": "Max", "staking_from": "Staking from", "advanced_details": "Advanced details", "ethereum_mainnet": "Ethereum Mainnet", "interacting_with": "Interacting with", "12_hours": "12 hours", "terms_of_service": "Terms of service", "risk_disclosure": "Risk disclosure", "cancel": "Cancel", "confirm": "Confirm", "continue": "Continue", "estimated_changes": "Estimated changes", "you_receive": "You receive", "up_to_n": "Up to {{count}}", "unstaking_to": "Unstaking to", "claiming_to": "Claiming to", "max_modal": {"title": "Max", "eth": {"description": "Max is the total amount of ETH you have, minus the gas fee required to stake. It’s a good idea to keep some extra ETH in your wallet for future transactions."}}, "use_max": "Use max", "estimated_unstaking_time": "1 to 11 days", "proceed_anyway": "Proceed anyway", "gas_cost_impact": "Gas cost impact", "select_a_token_to_deposit": "Select a token to deposit", "select_a_token_to_withdraw": "Select a token to withdraw", "you_could_earn_up_to": "You could earn up to", "per_year_on_your_tokens": "per year on your tokens", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "gas_cost_impact_warning": "Warning: the transaction gas cost will account for more than {{percentOverDeposit}}% of your deposit.", "earnings_history_title": "{{ticker}} earnings", "apr": "APR", "interactive_chart": {"timespan_buttons": {"7D": "7D", "1M": "1M", "3M": "3M", "6M": "6M"}}, "today": "Today", "one_week_average": "1 week average", "one_month_average": "1 month average", "three_month_average": "3 month average", "six_month_average": "6 month average", "one_year_average": "1 year average"}, "default_settings": {"title": "Your Wallet is ready", "description": "MetaMask uses default settings to best balance safety and ease of use. Change these settings to further increase your privacy.", "learn_more_about_privacy": "Learn more about privacy best practices.", "privacy_policy": "Privacy policy", "default_settings": "<PERSON><PERSON><PERSON>", "done": "Done", "basic_functionality": "Basic functionality", "manage_networks": "Choose your network", "manage_networks_body": "We use Infura as our remote procedure call (RPC) provider to offer the most reliable and private access to Ethereum data we can. You can choose your own RPC, but remember that any RPC will receive your IP address and Ethereum wallet to make transactions. Read our ", "manage_networks_body2": " to learn more about how Infura handles data for EVM accounts, for Solana accounts ", "manage_networks_body3": "click here.", "functionality_body": "MetaMask offers basic features like token details and gas settings through internet services. When you use internet services, your IP address is shared, in this case with MetaMask. This is just like when you visit any website. MetaMask uses this data temporarily and never sells your data. You can use a VPN or turn off these services, but it may affect your MetaMask experience. Read our ", "functionality_body2": " to learn more.", "sheet": {"title_off": "Turn off basic functionality", "description_off": "This means you won't fully optimize your time on MetaMask. Basic features (like token details, optimal gas settings, and others) won't be available to you.", "description_off2": "Turning this off also disables all features within", "description_off2_related_features1": "security and privacy, backup and sync", "description_off2_related_features1_and": "and", "description_off2_related_features2": "notifications.", "title_on": "Turn on basic functionality", "description_on": "To optimize your time on MetaMask, you’ll need to turn on this feature. Basic functions (like token details, optimal gas settings, notifications, and others) are important to the web3 experience.", "checkbox_label": "I understand and want to continue", "buttons": {"cancel": "Cancel", "turn_on": "Turn on", "turn_off": "Turn off", "reset": "Reset"}}, "drawer_general_title": "General", "drawer_general_title_desc": "Sync settings across devices, select network preferences, and track token data", "drawer_assets_title": "Assets", "drawer_assets_desc": "Autodetect tokens in your wallet, display NFTs, and get batched account balance updates", "drawer_security_title": "Security", "drawer_security_desc": "Reduce your chances of joining unsafe networks and protect your accounts", "network_details_check_desc": "MetaMask uses a third-party service called chainid.network to show accurate and standardized network details. This reduces your chances of connecting to malicious or incorrect network. When using this feature, your IP address is exposed to chainid.network."}, "simulation_details": {"failed": "There was an error loading your estimation.", "fiat_not_available": "Not Available", "incoming_heading": "You receive", "no_balance_changes": "No changes", "outgoing_heading": "You send", "reverted": "This transaction is likely to fail", "title": "Estimated changes", "tooltip_description": "Estimated changes are what might happen if you go through with this transaction. This is just a prediction, not a guarantee.", "total_fiat": "Total = {{currency}}"}, "spam_filter": {"block_origin_requests_for_1_minute": "Temporarily block this site", "cancel": "Cancel", "description": "If you're being spammed with multiple requests, you can temporarily block the site.", "got_it": "Got it", "site_blocked_description": "The site will be blocked for 1 minute.", "site_blocked_title": "You've temporarily blocked this site", "title": "We've noticed multiple requests"}, "common": {"please_wait": "Please wait", "disconnect_you_from": "This will disconnect you from {{dappUrl}}", "disconnect": "Disconnect"}, "tooltip_modal": {"reward_rate": {"title": "Reward rate", "tooltip": "Expected yearly increase in the value of your stake, based on the reward rate over the past week."}, "estimated_gas_fee": {"title": "Estimated gas fee", "gas_recipient": "Gas fees are paid to crypto miners who process transactions on Ethereum network. Metamask does not profit from gas fees.", "gas_fluctuation": "Gas fees are estimated and will fluctuate based on network traffic and transaction complexity.", "gas_learn_more": "Learn more about gas fees"}, "reward_frequency": {"title": "Reward frequency", "tooltip": "Your staked balance updates every 12 hours to account for new rewards."}, "unstaking_time": {"title": "Unstaking time", "tooltip": "It typically takes less than 3 days to unstake your ETH, but it can take up to 11 days to process. The exact duration depends on the amount you unstake and ETH staking activity"}}, "confirm": {"cancel": "Cancel", "confirm": "Confirm", "staking_footer": {"part1": "By continuing, you agree to our ", "terms_of_use": "Terms of Use", "part2": " and ", "risk_disclosure": "Risk Disclosure", "part3": "."}, "label": {"amount": "Amount", "value": "Value", "account": "Account", "balance": "Balance", "interacting_with": "Interacting with", "network": "Network", "primary_type": "Primary type", "request_from": "Request from", "signing_in_with": "Signing in with", "spender": "<PERSON>pender", "now": "Now", "switching_to": "Switching To", "bridge_estimated_time": "Est. time", "pay_with": "Pay with", "total": "Total", "transaction_fee": "Transaction fee", "metamask_fee": "MetaMask fee", "network_fee": "Network fee", "bridge_fee": "Bridge provider fee"}, "title": {"signature": "Signature request", "permit": "Spending cap request", "permit_revoke": "Remove permission", "permit_NFTs": "<PERSON><PERSON><PERSON> request", "signature_siwe": "Sign-in request", "contract_interaction": "Transaction request", "contract_deployment": "Deploy a contract", "transfer": "Transfer request", "switch_account_type": "Account update", "approve": "Approve request", "perps_deposit": "Add funds"}, "sub_title": {"permit": "This site wants permission to spend your tokens.", "permit_revoke": "You're removing someone's permission to spend tokens from your account.", "permit_NFTs": "This site wants permission to withdraw your NFTs.", "permit_revoke_NFTs": "This site would like to reset the withdraw limit for your NFTs", "signature": "Review request details before you confirm.", "signature_siwe": "A site wants you to sign in to prove you own this account.", "contract_interaction": "Review request details before you confirm.", "switch_to_smart_account": "You're switching to a smart account.", "switch_to_standard_account": "You're switching back to a standard account (EOA).", "contract_deployment": "This site wants you to deploy a contract", "decrease_allowance": "This site wants decrease the spending cap for your tokens."}, "tooltip": {"perps_deposit": {"transaction_fee": "We'll swap your tokens for USDC on HyperEVM, the network used by Perps. Swap providers may charge a fee, but MetaMask won't."}, "title": {"transaction_fee": "Fees"}}, "spending_cap": "Spending cap", "withdraw": "Withdraw", "nfts": "NFTs", "permission_from": "Permission from", "spender": "<PERSON>pender", "request_from": "Request from", "staking_from": "Staking from", "signing_in_with": "Signing in with", "message": "Message", "personal_sign_tooltip": "This site is asking for your signature", "transaction_tooltip": "This site is asking for your transaction", "details": "Details", "qr_get_sign": "Get Signature", "qr_scan_text": "Scan with your hardware wallet", "sign_with_ledger": "Sign with <PERSON><PERSON>", "smart_account": "Smart Account", "smart_contract": "Smart contract", "standard_account": "Standard Account", "siwe_message": {"url": "URL", "network": "Network", "account": "Account", "version": "Version", "chain_id": "Chain ID", "nonce": "<PERSON><PERSON>", "issued": "Issued", "requestId": "Request ID", "resources": "Resources"}, "simulation": {"decoded_tooltip_bid_nft": "The NFT will be reflected in your wallet, when the bid is accepted.", "decoded_tooltip_list_nft": "Expect changes only if someone buys your NFTs.", "edit_value_balance_info": "Account balance:", "info_permit": "You're giving the spender permission to spend this many tokens from your account.", "info_revoke": "You're removing someone's permission to spend tokens from your account.", "label_change_type_bidding": "You bid", "label_change_type_listing": "You list", "label_change_type_nft_listing": "Listing price", "label_change_type_permit": "Spending cap", "label_change_type_permit_nft": "Withdraw", "label_change_type_receive": "You receive", "label_change_type_revoke": "Revoke", "label_change_type_transfer": "You send", "label_change_type_approve": "You approve", "personal_sign_info": "You're signing into a site and there are no predicted changes to your account.", "title": "Estimated changes", "tooltip": "Estimated changes are what might happen if you go through with this transaction. This is just a prediction, not a guarantee.", "unavailable": "Unavailable"}, "7702_functionality": {"smartAccountLabel": "Smart Account", "standardAccountLabel": "Standard Account", "switch": "Switch", "switchBack": "Switch back", "splashpage": {"accept": "Yes", "betterTransaction": "Faster transactions, lower fees", "betterTransactionDescription": "Save time and money by processing transactions together.", "featuresDescription": "Keep the same account address, and you can switch back anytime.", "payToken": "Pay with any token, any time", "payTokenDescription": "Use the tokens you already have to cover network fees.", "reject": "No", "sameAccount": "Same account, smarter features.", "splashTitle": "Use smart account?"}, "includes_transaction": "Includes {{transactionCount}} transactions", "useSmartAccount": "Use smart account", "successful": "Successful!", "success_message": "Your account will be updated to smart account with your next transaction."}, "edit_spending_cap_modal": {"account_balance": "Account balance", "cancel": "Cancel", "description": "Enter the amount that you feel comfortable being spent on your behalf.", "invalid_number_error": "Spending cap must be a number", "no_empty_error": "Spending cap cannot be empty", "no_extra_decimals_error": "Spending cap cannot have more decimals than the token", "no_zero_error": "Spending cap cannot be 0", "no_zero_error_decrease_allowance": "0 spending cap has no effect on 'decreaseAllowance' method", "no_zero_error_increase_allowance": "0 spending cap has no effect on 'increaseAllowance' method", "save": "Save", "title": "Edit approval limit"}, "unlimited": "Unlimited", "all": "All", "none": "None", "advanced_details": "Advanced details", "interacting_with": "Interacting with", "data": "Data", "review": "Review", "transferRequest": "Transfer request", "nested_transaction_heading": "Transaction {{index}}", "transaction": "Transaction", "available_balance": "Available: ", "edit_amount_done": "Done", "deposit_edit_amount_done": "Continue"}, "change_in_simulation_modal": {"title": "Results have changed", "description": "Estimated changes for this transaction have been updated. Review them closely before proceeding.", "proceed": "Proceed", "reject": "Reject the transaction"}, "snap_account_custom_name_approval": {"title": "Add account to MetaMask", "input_title": "Account name", "add_account_button": "Add account", "name_taken_message": "This account name already exists"}, "smart_transactions_migration": {"title": "Transactions just got smarter", "link": "Higher success rates", "description": " and MEV protection. Now on by default."}, "bridge": {"continue": "Continue", "confirm_bridge": "Bridge", "confirm_swap": "<PERSON><PERSON><PERSON>", "terms_and_conditions": "Terms & Conditions", "select_token": "Select token", "select_network": "Select network", "all_networks": "All networks", "num_networks": "{{numNetworks}} networks", "one_network": "1 network", "select_all_networks": "Select all", "deselect_all_networks": "Deselect all", "see_all": "See all", "apply": "Apply", "slippage": "Slippage", "slippage_info": "If the price changes between the time your order is placed and confirmed it’s called “slippage.” Your swap will automatically cancel if slippage exceeds the tolerance you set here.", "network_fee": "Network Fee", "included": "Included", "estimated_time": "Estimated Time", "quote": "Quote", "rate": "Rate", "quote_details": "Quote Det<PERSON>", "price_impact": "Price Impact", "time": "Time", "quote_info_content": "The best rate we found from providers, including provider fees and a 0.875% MetaMask fee.", "quote_info_title": "Rate", "network_fee_info_title": "Network Fee", "network_fee_info_content": "Network fees depend on how busy the network is and how complex your transaction is.", "points": "Points", "points_tooltip": "Points", "points_tooltip_content": "Estimate of MetaMask Rewards Points you will earn from this trade. Points can take up to 1 hour to be confirmed in your Rewards balance", "unable_to_load": "Unable to load", "points_error": "We can't load points right now", "points_error_content": "You'll still earn any points for this transaction. We'll notify you once they've been added to your account. You can also check your rewards tab in about an hour.", "see_other_quotes": "See other quotes", "receive_at": "Receive at", "error_banner_description": "This trade route isn't available right now. Try changing the amount, network, or token and we'll find the best option.", "insufficient_funds": "Insufficient funds", "insufficient_gas": "Insufficient gas", "select_amount": "Select amount", "bridge_to": "Bridge to", "swap_to": "Swap to", "title": "Bridge", "submitting_transaction": "Submitting", "fetching_quote": "Fetching quote", "fee_disclaimer": "Includes 0.875% MM fee", "hardware_wallet_not_supported": "Hardware wallets aren't supported yet. Use a hot wallet to continue.", "hardware_wallet_not_supported_solana": "Hardware wallets aren't supported for Solana yet. Use a hot wallet to continue.", "price_impact_info_title": "Price Impact", "price_impact_info_description": "Price impact reflects how your swap order affects the market price of the asset. It depends on the trade size and the available liquidity in the pool. MetaMask does not influence or control price impact.", "price_impact_info_gasless_description": "Price impact reflects how your swap order affects the market price of the asset. If you don't hold enough funds for gas, part of your source token is automatically allocated to cover fees, which increases price impact. MetaMask does not influence or control price impact.", "slippage_info_title": "Slippage", "slippage_info_description": "The % change in price you're willing to allow before your transaction is canceled.", "blockaid_error_title": "This transaction will be reverted", "max": "Max", "approval_needed": "This will approve {{amount}} {{symbol}} for swapping.", "approval_tooltip_title": "Grant exact access", "approval_tooltip_content": "You are allowing access to the specified amount, {{amount}} {{symbol}}. The contract will not access any additional funds.", "minimum_received": "Minimum Received", "minimum_received_tooltip_title": "Minimum Received", "minimum_received_tooltip_content": "The minimum amount you'll receive if the price changes while your transaction is processing, based on your slippage tolerance. This is an estimate from our liquidity providers. Final amounts may differ."}, "quote_expired_modal": {"title": "New quotes are available", "description": "Rates update every {{refreshRate}} seconds, so tap Get new quote when you're ready.", "get_new_quote": "Get new quote"}, "blockaid_modal": {"simulation_title": "Transaction failed in simulation", "validation_title": "Transaction failed validation", "go_back": "Go back"}, "bridge_transaction_details": {"status": "Status", "date": "Date", "total_gas_fee": "Total gas fee", "estimated_completion": "Est. completion", "bridge_step_action_bridge_complete": "{{destSymbol}} received on {{destChainName}}", "bridge_step_action_bridge_pending": "Receiving {{destSymbol}} on {{destChainName}}", "bridge_step_action_swap_complete": "Swapped {{srcSymbol}} for {{destSymbol}}", "bridge_step_action_swap_pending": "Swapping {{srcSymbol}} for {{destSymbol}}", "view_on_block_explorer": "View on Block Explorer", "block_explorer_description": "This transaction lives on two networks. The first link shows the source; the second shows the destination once it’s confirmed.", "transaction_details": "Transaction Details", "bridge_to_chain": "Bridge to {{chainName}}", "recipient": "Recipient"}, "account_status": {"account_already_exists": "Wallet already exists", "account_already_exists_description": "A wallet using \"{{accountName}}\" already exists. Do you want to try logging in instead?", "log_in": "Log in", "account_not_found": "Wallet not found", "account_not_found_description": "We couldn’t find a wallet for \"{{accountName}}\". Do you want to create a new one with this login?", "create_new_wallet": "Create a new wallet", "use_different_login_method": "Use a different login method"}, "error_sheet": {"still_there_title": "Still there?", "still_there_description": "Your login timed out due to inactivity. Try again when you’re ready.", "unable_to_login_title": "Unable to connect", "unable_to_login_description": "Your internet connection is unstable. Check your connection and try again.", "something_went_wrong_title": "Something went wrong", "something_went_wrong_description": "An error occurred while logging in. Try again and if the issue continues, contact", "support_button": "MetaMask Support.", "error_button": "Try again", "user_cancelled_title": "Login cancelled", "user_cancelled_description": "You cancelled the login process.\nTry again when you’re ready.", "user_cancelled_button": "Try again", "google_login_no_credential_title": "Google login failed", "google_login_no_credential_description": "We couldn’t find a Google account associated with this login. Try again with a different login method.", "google_login_no_credential_button": "Try again", "oauth_error_title": "<PERSON><PERSON> failed", "oauth_error_description": "An error occurred while logging in.\nTry again and if the issue continues, contact MetaMask Support.", "oauth_error_button": "Try again", "no_internet_connection_title": "Unable to connect", "no_internet_connection_description": "Your internet connection is unstable. Check your connection and try again.", "no_internet_connection_button": "Try again"}, "password_hint": {"title": "Password hint", "description": "Leave yourself a hint to help remember your password. This hint is stored on your device, and won’t be shared.", "description2": "Remember: If you lose your password, you won’t be able to use your wallet.", "button": "Create hint", "placeholder": "e.g. mom’s home", "saved": "Save", "saved_toast": "Password hint updated", "error_matches_password": "You cannot use your password as a hint"}, "protect_your_wallet": {"title": "Wallet Recovery", "login_with_social": "Log in with Social Accounts", "setup": "Set Up", "secret_recovery_phrase": "Secret recovery phrase {{num}}", "back_up": "Back up", "reveal": "Reveal", "social_recovery_title": "{{authConnection}} RECOVERY", "social_recovery_enable": "Enabled", "social_login_description": "Use your {{authConnection}} login and MetaMask password to recover your account and secret recovery phrases.", "srps_title": "SECRET RECOVERY PHRASES", "srps_description": "Your wallet is most protected when both recovery methods are set up. If one fail, the other helps you recover your wallet."}, "backupAndSync": {"title": "Backup and sync", "description": "Back up your accounts and sync settings.", "enabling": "Enabling backup and sync", "disabling": "Disabling backup and sync", "enable": {"title": "Turn on backup and sync", "confirmation": "When you turn on backup and sync, you’re also turning on basic functionality. Do you want to continue?", "description": "Backup and sync lets us store encrypted data for your custom settings and features. This keeps your MetaMask experience the same across devices and restores settings and features if you ever need to reinstall MetaMask. This doesn’t back up your Secret Recovery Phrase.", "updatePreferences": "You can update your preferences at any time in", "settingsPath": "Settings > Backup and sync."}, "privacyLink": "Learn how we protect your privacy", "features": {"accounts": "Accounts", "contacts": "Contacts"}, "manageWhatYouSync": {"title": "Manage what you sync", "description": "Turn on what’s synced between your devices."}}, "snap_ui": {"asset_selector": {"title": "Select an asset"}, "account_selector": {"title": "Select account"}, "dropdown": {"title": "Select an option"}, "hideSentitiveInfo": {"message": "Hide sensitive information"}, "doNotShare": {"message": "Do not share this with anyone"}, "revealSensitiveContent": {"message": "Reveal sensitive content"}, "show_more": "more", "show_less": "less"}, "multichain_accounts": {"intro": {"title": "Introducing multichain accounts", "section_1_title": "What are multichain accounts?", "section_1_description": "One account, addresses on multiple networks MetaMask supports. So now you can use Ethereum, Solana, and more without switching accounts.", "section_2_title": "Same address, more networks", "section_2_description": "We’ve merged your accounts. You can keep using MetaMask the same way as before. Your funds are safe and unchanged.", "view_accounts_button": "View accounts", "learn_more_button": "Learn more"}, "learn_more": {"title": "Learn more", "description": "Multichain accounts are now the default. To opt out, turn off basic functionality.", "checkbox_label": "Turn off basic functionality", "confirm_button": "Confirm"}, "add_wallet": "Add wallet", "add_hardware_wallet": "Add a hardware wallet", "account_details": {"header_title": "Account Details", "account_name": "Account Name", "networks": "Networks", "account_address": "Account Address", "wallet": "Wallet", "private_key": "Private key", "private_keys": "Private keys", "unlock_to_reveal": "Unlock to reveal", "smart_account": "Smart account", "set_up": "Set up", "secret_recovery_phrase": "Secret Recovery Phrase", "back_up": "Back up", "remove_account": "Remove account"}, "address_list": {"addresses": "Addresses", "receiving_address": "Receiving address", "copied": "Address copied"}, "private_key_list": {"list_title": "Private keys", "warning_title": "Don't share your private key", "warning_description": "This key grants full control of your account for the associated chain.", "learn_more": "Learn more", "enter_password": "Enter your password", "password_placeholder": "Password", "wrong_password": "Wrong password", "copied": "Private key copied", "continue": "Continue", "cancel": "Cancel"}, "accounts_list": {"details": "Details"}, "wallet_details": {"wallet_name": "Wallet Name", "balance": "Balance", "create_account": "Create account", "creating_account": "Creating account...", "back_up": "Back up", "reveal_recovery_phrase_with_index": "Reveal Recovery Phrase {{index}}"}, "smart_account": {"title": "Enable Smart Account", "description": "You can enable smart account features on supported networks.", "learn_more": "Learn more"}, "edit_account_name": {"title": "Edit Account Name", "account_name": "Account name", "confirm_button": "Confirm", "name": "Name", "save_button": "Save", "error": "Failed to edit account name", "error_duplicate_name": "This name is already in use.", "error_empty_name": "Account name cannot be empty"}, "delete_account": {"title": "Remove Account", "warning_title": "This account will be removed from MetaMask.", "warning_description": "Make sure you have the Secret Recovery Phrase or private key for this account before removing.", "remove_button": "Remove", "cancel_button": "Cancel", "error": "Failed to delete account"}, "share_address": {"title": "Share Address", "copy_address": "Copy address", "view_on_explorer_button": "View on {{explorer}}", "view_on_block_explorer": "View on Block Explorer"}, "share_address_qr": {"title": "{{networkName}} Address", "copy_address": "Copy address", "description": "Use this address to receive tokens and collectibles on", "description_prefix": "Use this address to receive tokens and collectibles on"}, "export_credentials": {"export_private_key": "Private key", "private_key_warning_title": "Never disclose this key.", "private_key_warning_description": "Anyone with your private key can steal any assets held in your account.", "credential_as_text": "Text", "credential_as_qr": "QR code", "export_mnemonic": "Export mnemonic", "backup": "Backup"}, "reveal_private_key": {"title": "Show private key", "banner_title": "Never disclose this key.", "banner_description": "Anyone with your private key can steal any assets held in your account.", "enter_password": "Enter your password", "password_placeholder": "Password", "next": "Next", "copy": "Copy"}, "reveal_srp": {"header": "Security quiz", "description": "To reveal your Secret Recovery Phrase, you need to correctly answer two questions", "get_started": "Get started", "learn_more": "Learn more"}, "address_rows_list": {"search_placeholder": "Search networks", "no_networks_found": "No networks found", "no_networks_available": "No networks available"}}, "deep_link_modal": {"private_link": {"title": "Redirecting you to MetaMask", "description": "You'll open {{pageTitle}} if you continue.", "checkbox_label": "Don't remind me again"}, "public_link": {"title": "Proceed with caution", "description": "You were sent here by a third party, not MetaMask. You'll open {{pageTitle}} if you continue."}, "invalid": {"title": "This page doesn't exist", "description": "We can't find the page you're looking for.", "update_to_store_link": "Update to the latest version of MetaMask", "well_take_you_to_right_place": " and we'll take you to the right place."}, "go_to_home_button": "Go to the home page", "back_button": "Back", "continue_button": "Continue"}, "card": {"card": "MetaMask Card", "add_funds_bottomsheet": {"deposit": "Fund with cash", "deposit_description": "Low-cost card or bank transfer", "swap": "Fund with crypto", "swap_description": "Swap tokens into {{symbol}} on Linea", "select_method": "Select method"}, "card_home": {"error_title": "Can’t fetch data", "error_description": "It seems that there is an issue preventing you from viewing the content on this page. Please check your connection or try refreshing the page.", "try_again": "Try again", "limited_spending_warning": "Your actual spending ability may be limited. To adjust your limit, go to {{manageCard}}", "add_funds": "Add funds", "manage_card_options": {"manage_card": "Manage card", "advanced_card_management_description": "See transactions, freeze card, and more"}}}, "onboarding_error_fallback": {"title": "An error occurred", "description": "Send us an error report to help fix the problem and improve MetaMask. It will be confidential and anonymous.", "recovery_warning": "If you keep getting this error, save your Secret Recovery Phrase and re-install the app. Remember: without your Secret Recovery Phrase, you can't restore your wallet.", "error_message_report": "Error report:", "copy": "Copy", "send_report": "Send report", "try_again": "Try again", "report_submitted": "Error report has been submitted."}, "pay_with_modal": {"title": "Select payment method"}, "connection_removed_modal": {"title": "Connections removed", "content": "Some connections (like hardware wallets and snaps) were removed due to inactivity on this device. You can re-add them anytime in Settings.", "tryAgain": "Try again", "close": "Close"}, "rewards": {"auth_fail_title": "Unknown Error.", "auth_fail_description": "An unknown error occurred while authenticating this account with the rewards program. Please try again later.", "failed_to_authenticate": "Failed to authenticate with rewards program", "not_implemented": "Coming soon", "not_implemented_season_summary": "Season Summary Coming Soon", "referral_rewards_title": "Referrals", "points": "Points", "point": "Point", "level": "Level", "to_level_up": "To level up", "season_ends": "Season ends", "season_ended": "Season ended", "main_title": "Rewards", "referral_title": "Referrals", "tab_overview_title": "Overview", "tab_activity_title": "Activity", "tab_levels_title": "Levels", "referral_stats_earned_from_referrals": "Earned from referrals", "referral_stats_referrals": "Referrals", "loading_activity": "Loading activity...", "error_loading_activity": "Error loading activity", "activity_empty_title": "No recent activity.", "activity_empty_description": "Use MetaMask to earn points, level up, and unlock rewards.", "activity_empty_link": "See ways to earn", "toast_dismiss": "<PERSON><PERSON><PERSON>", "events": {"type": {"swap": "<PERSON><PERSON><PERSON>", "perps": "Perps", "referral": "Referral", "referral_action": "Referral action", "sign_up_bonus": "Sign up bonus", "loyalty_bonus": "Loyalty bonus", "one_time_bonus": "One-time bonus", "open_position": "Opened position", "close_position": "Closed position", "take_profit": "Take profit", "stop_loss": "Stop loss", "uncategorized_event": "Uncategorized event"}}, "onboarding": {"not_supported_region_title": "Region not supported", "not_supported_region_description": "Rewards are not supported in your region yet. We are working on expanding access, so check back later.", "not_supported_account_needed_title": "Ethereum account needed", "not_supported_account_needed_description": "MetaMask Rewards aren't available for Solana accounts yet. Switch to an Ethereum account to claim your points.", "not_supported_confirm": "Got it", "intro_title_1": "Season 1", "intro_title_2": "is Live", "intro_description": "Earn points for your your activity. \nAdvance through levels to unlock rewards.", "intro_confirm": "<PERSON><PERSON><PERSON> 250 points", "intro_confirm_geo_loading": "Checking region...", "checking_opt_in": "Checking opt-in for accounts...", "redirecting_to_dashboard": "Redirecting to dashboard...", "intro_skip": "Not now", "step_confirm": "Next", "step1_title": "Earn points on every trade", "step1_description": "Every swap and perps trade you make in MetaMask gets you closer to rewards. Link your accounts and watch your points add up.", "step2_title": "Level up for bigger perks", "step2_description": "Hit points milestones to get perks like 50% off perps fees, exclusive tokens, and a free MetaMask Metal Card.", "step3_title": "Exclusive seasonal rewards", "step3_description": "Each season brings new perks. Join in, compete, and claim what you can before time runs out.", "step4_title": "You'll earn 250 points when you sign up!", "step4_title_referral_bonus": "You'll earn 500 points when you sign up with a code!", "step4_title_referral_validating": "Validating referral code...", "step4_referral_bonus_description": "Use a referral code to earn 250 points", "step4_referral_input_placeholder": "Referral code (optional)", "step4_confirm": "<PERSON>laim points", "step4_confirm_loading": "Claiming points...", "step4_linking_accounts": "Linking accounts... ({{current}}/{{total}})", "step4_linking_accounts_loading": "Linking additional accounts...", "step4_success_description": "You have successfully signed up for MetaMask Rewards!", "step4_legal_disclaimer": "Joining means we'll track your on-chain activity to reward you automatically.", "step4_legal_disclaimer_learn_more": "Learn more."}, "settings": {"title": "<PERSON><PERSON>s", "subtitle": "Connect multiple accounts to combine your points and unlock rewards faster.", "error_title": "Unable to Load Linked Accounts", "error_description": "We couldn't detect your linked accounts. Please check your internet connection and try again.", "error_retry": "Retry", "tab_linked_accounts": "Linked Accounts ({{count}})", "tab_unlinked_accounts": "Unlinked Accounts ({{count}})", "no_linked_accounts": "No linked accounts", "all_accounts_linked_title": "All accounts are linked", "all_accounts_linked_description": "You have linked all your accounts to the rewards program.", "link_account_success_title": "Account {{accountName}} successfully linked", "link_account_error_title": "Failed to link account", "link_account_button": "Link"}, "optout": {"title": "Opt out of Rewards", "description": "This will erase your points and progress. You won't be able to undo this.", "confirm": "Opt out", "modal": {"confirmation_title": "Are you sure?", "confirmation_description": "This will remove all your progress, and can't be reversed. If you rejoin the Rewards program later, you'll restart at 0.", "cancel": "Cancel", "confirm": "Confirm", "error_message": "Failed to opt out of rewards program. Please try again.", "processing": "Processing..."}}, "unlinked_accounts_info": {"title": "There are unlinked accounts", "description": "If you want to earn points for the activity of these accounts you can link them in the settings page.", "go_to_settings": "Go to settings"}, "unlinked_account_info": {"title": "Account not linked", "description": "This account's activity is not being tracked for this season."}, "link_account": "Link account", "linking_account": "Linking...", "ways_to_earn": {"title": "Ways to earn", "supported_networks": "Supported Networks", "swap": {"title": "<PERSON><PERSON><PERSON>", "sheet_title": "Swap tokens", "sheet_description": "Swap tokens on supported networks to earn points for every dollar you trade.", "points": "80 points per $100", "cta_label": "Start a swap"}, "perps": {"title": "Perps", "sheet_title": "Trade perps", "sheet_description": "Earn points on every trade, including opens and closes, stop loss and take profit orders, and margin adjustments.", "points": "10 points per $100", "cta_label": "Start a trade"}, "referrals": {"title": "Referrals", "points": "Get 20% from friends you refer"}}, "referral": {"actions": {"share_referral_link": "Refer a friend", "share_referral_subject": "Join <PERSON>"}, "info": {"title": "Share your code to earn more", "description": "Your friends earn bonus points, and you get 20% of their rewards."}}, "active_boosts_title": "Active Boosts", "season_1": "Season 1"}, "time": {"minutes_format": "{{count}} minute", "minutes_format_plural": "{{count}} minutes"}, "transaction_details": {"title": {"perps_deposit": "Funded perps account", "default": "Transaction details"}, "label": {"bridge_fee": "Bridge fee", "network_fee": "Network fee", "paid_with": "Paid with", "total": "Total"}, "summary_title": {"bridge": "Bridge from {{sourceSymbol}} to {{targetSymbol}}", "bridge_approval": "Approve {{approveSymbol}}", "default": "Transaction", "perps_deposit": "Add funds", "swap": "Swap tokens", "swap_approval": "Approve tokens"}}}