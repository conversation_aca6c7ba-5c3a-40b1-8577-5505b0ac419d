{"KeyringController": {"isUnlocked": false, "keyrings": []}, "LoggingController": {"logs": {}}, "AccountTrackerController": {"accountsByChainId": {"0x1": {}}}, "AccountsController": {"internalAccounts": {"accounts": {}, "selectedAccount": ""}}, "AccountTreeController": {"accountTree": {"selectedAccountGroup": "", "wallets": {}}, "accountGroupsMetadata": {}, "accountWalletsMetadata": {}, "hasAccountTreeSyncingSyncedAtLeastOnce": false, "isAccountTreeSyncingInProgress": false}, "AddressBookController": {"addressBook": {}}, "NftController": {"allNftContracts": {}, "allNfts": {}, "ignoredNfts": []}, "TokensController": {"allTokens": {}, "allIgnoredTokens": {}, "allDetectedTokens": {}}, "TokenListController": {"tokensChainsCache": {}, "preventPollingOnNetworkRestart": false}, "PPOMController": {"storageMetadata": [], "versionInfo": []}, "CurrencyRateController": {"currentCurrency": "usd", "currencyRates": {"ETH": {"conversionDate": 0, "conversionRate": 0, "usdConversionRate": null}}}, "NetworkController": {"selectedNetworkClientId": "mainnet", "networksMetadata": {"mainnet": {"status": "unknown", "EIPS": {}}}, "networkConfigurationsByChainId": {"0x1": {"blockExplorerUrls": [], "chainId": "0x1", "defaultRpcEndpointIndex": 0, "name": "Ethereum Mainnet", "nativeCurrency": "ETH", "rpcEndpoints": [{"networkClientId": "mainnet", "type": "infura", "url": "https://mainnet.infura.io/v3/{infuraProjectId}", "failoverUrls": []}]}, "0x18c6": {"blockExplorerUrls": ["https://megaexplorer.xyz"], "chainId": "0x18c6", "defaultRpcEndpointIndex": 0, "defaultBlockExplorerUrlIndex": 0, "name": "Mega Testnet", "nativeCurrency": "MegaETH", "rpcEndpoints": [{"networkClientId": "megaeth-testnet", "type": "custom", "url": "https://carrot.megaeth.com/rpc", "failoverUrls": []}]}, "0x279f": {"blockExplorerUrls": ["https://testnet.monadexplorer.com"], "chainId": "0x279f", "defaultBlockExplorerUrlIndex": 0, "defaultRpcEndpointIndex": 0, "name": "<PERSON>d Testnet", "nativeCurrency": "MON", "rpcEndpoints": [{"failoverUrls": [], "networkClientId": "monad-testnet", "type": "custom", "url": "https://testnet-rpc.monad.xyz"}]}, "0xaa36a7": {"blockExplorerUrls": [], "chainId": "0xaa36a7", "defaultRpcEndpointIndex": 0, "name": "Sepolia", "nativeCurrency": "SepoliaETH", "rpcEndpoints": [{"networkClientId": "sepolia", "type": "infura", "url": "https://sepolia.infura.io/v3/{infuraProjectId}", "failoverUrls": []}]}, "0xe705": {"blockExplorerUrls": [], "chainId": "0xe705", "defaultRpcEndpointIndex": 0, "name": "Linea Sepolia", "nativeCurrency": "LineaETH", "rpcEndpoints": [{"networkClientId": "linea-sepolia", "type": "infura", "url": "https://linea-sepolia.infura.io/v3/{infuraProjectId}", "failoverUrls": []}]}, "0xe708": {"blockExplorerUrls": [], "chainId": "0xe708", "defaultRpcEndpointIndex": 0, "name": "Linea", "nativeCurrency": "ETH", "rpcEndpoints": [{"networkClientId": "linea-mainnet", "type": "infura", "url": "https://linea-mainnet.infura.io/v3/{infuraProjectId}", "failoverUrls": []}]}, "0x2105": {"blockExplorerUrls": [], "chainId": "0x2105", "defaultRpcEndpointIndex": 0, "name": "Base Mainnet", "nativeCurrency": "ETH", "rpcEndpoints": [{"failoverUrls": [], "networkClientId": "base-mainnet", "type": "infura", "url": "https://base-mainnet.infura.io/v3/{infuraProjectId}"}]}, "0xf1ac2": {"blockExplorerUrls": ["https://nnxscan.io"], "chainId": "0xf1ac2", "defaultRpcEndpointIndex": 0, "defaultBlockExplorerUrlIndex": 0, "name": "NeoNix Mainnet", "nativeCurrency": "NNX", "rpcEndpoints": [{"failoverUrls": [], "networkClientId": "neonix-mainnet", "type": "custom", "url": "https://rpc.nnxscan.io"}]}}}, "NetworkEnablementController": {"enabledNetworkMap": {"bip122": {"bip122:000000000019d6689c085ae165831e93": true, "bip122:000000000933ea01ad0ee984209779ba": false, "bip122:00000008819873e925422c1ff0f99f7c": false}, "eip155": {"0x1": true, "0x2105": true, "0xe708": true}, "solana": {"solana:4uhcVJyU9pJkvQyS88uRDiswHXSCkY3z": false, "solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp": true, "solana:EtWTRABZaYq6iMfeYKouRu166VU2xqa1": false}}}, "PhishingController": {"c2DomainBlocklistLastFetched": 0, "phishingLists": [], "whitelist": [], "hotlistLastFetched": 0, "stalelistLastFetched": 0, "urlScanCache": {}}, "PreferencesController": {"featureFlags": {}, "identities": {}, "ipfsGateway": "https://dweb.link/ipfs/", "lostIdentities": {}, "selectedAddress": "", "useTokenDetection": true, "useNftDetection": true, "useSafeChainsListValidation": true, "displayNftMedia": true, "dismissSmartAccountSuggestionEnabled": false, "securityAlertsEnabled": true, "isMultiAccountBalancesEnabled": true, "showMultiRpcModal": false, "showTestNetworks": false, "smartAccountOptIn": true, "smartAccountOptInForAccounts": [], "showIncomingTransactions": {"0x1": true, "0x5": true, "0x38": true, "0x61": true, "0xa": true, "0xa869": true, "0xaa37dc": true, "0x89": true, "0x13881": true, "0xa86a": true, "0xfa": true, "0xfa2": true, "0xaa36a7": true, "0xe704": true, "0xe705": true, "0xe708": true, "0x504": true, "0x507": true, "0x505": true, "0x64": true, "0x531": true}, "isIpfsGatewayEnabled": true, "smartTransactionsOptInStatus": true, "useTransactionSimulations": true, "tokenSortConfig": {"key": "tokenFiatAmount", "order": "dsc", "sortCallback": "stringNumeric"}, "tokenNetworkFilter": {}, "privacyMode": false}, "TokenBalancesController": {"tokenBalances": {}}, "TokenRatesController": {"marketData": {}}, "TokenSearchDiscoveryController": {"lastSearchTimestamp": null, "recentSearches": []}, "TokenSearchDiscoveryDataController": {"swapsTokenAddressesByChainId": {}, "tokenDisplayData": []}, "TransactionController": {"lastFetchedBlockNumbers": {}, "methodData": {}, "submitHistory": [], "transactions": [], "transactionBatches": []}, "SmartTransactionsController": {"smartTransactionsState": {"smartTransactions": {"0x1": []}, "userOptIn": null, "userOptInV2": null, "fees": {"approvalTxFees": null, "tradeTxFees": null}, "liveness": true, "livenessByChainId": {"0x1": true, "0xaa36a7": true}, "feesByChainId": {"0x1": {"approvalTxFees": null, "tradeTxFees": null}, "0xaa36a7": {"approvalTxFees": null, "tradeTxFees": null}}}}, "SnapController": {"snapStates": {}, "snaps": {}, "unencryptedSnapStates": {}}, "SnapInterfaceController": {"interfaces": {}}, "SnapsRegistry": {"database": null, "databaseUnavailable": false, "lastUpdated": null}, "SubjectMetadataController": {"subjectMetadata": {}}, "SwapsController": {"quotes": {}, "quoteValues": {}, "fetchParams": {"slippage": 0, "sourceToken": "", "sourceAmount": 0, "destinationToken": "", "walletAddress": ""}, "fetchParamsMetaData": {"sourceTokenInfo": {"decimals": 0, "address": "", "symbol": ""}, "destinationTokenInfo": {"decimals": 0, "address": "", "symbol": ""}, "networkClientId": "mainnet"}, "topAggSavings": null, "aggregatorMetadata": null, "tokens": null, "topAssets": null, "approvalTransaction": null, "aggregatorMetadataLastFetched": 0, "quotesLastFetched": 0, "error": {"key": null, "description": null}, "topAggId": null, "tokensLastFetched": 0, "isInPolling": false, "pollingCyclesLeft": 3, "quoteRefreshSeconds": null, "usedGasEstimate": null, "usedCustomGas": null, "chainCache": {"0x1": {"aggregatorMetadata": null, "tokens": null, "topAssets": null, "aggregatorMetadataLastFetched": 0, "topAssetsLastFetched": 0, "tokensLastFetched": 0}}}, "GasFeeController": {"gasFeeEstimates": {}, "estimatedGasFeeTimeBounds": {}, "gasEstimateType": "none", "gasFeeEstimatesByChainId": {}, "nonRPCGasFeeApisDisabled": false}, "ApprovalController": {"pendingApprovals": {}, "pendingApprovalCount": 0, "approvalFlows": []}, "PermissionController": {"subjects": {}}, "PerpsController": {"activeProvider": "hyperliquid", "isTestnet": false, "connectionStatus": "disconnected", "positions": [], "accountState": null, "pendingOrders": [], "depositInProgress": false, "lastDepositTransactionId": null, "lastDepositResult": null, "lastWithdrawResult": null, "withdrawInProgress": false, "lastError": null, "lastUpdateTimestamp": 0, "isEligible": false, "isFirstTimeUser": {"testnet": true, "mainnet": true}, "hasPlacedFirstOrder": {"testnet": false, "mainnet": false}, "perpsBalances": {}}, "RemoteFeatureFlagController": {"cacheTimestamp": 0, "remoteFeatureFlags": {}}, "SeedlessOnboardingController": {"isSeedlessOnboardingUserAuthenticated": false, "socialBackupsMetadata": []}, "SelectedNetworkController": {"domains": {}, "activeDappNetwork": null}, "SignatureController": {"signatureRequests": {}, "unapprovedPersonalMsgs": {}, "unapprovedTypedMessages": {}, "unapprovedPersonalMsgCount": 0, "unapprovedTypedMessagesCount": 0}, "AuthenticationController": {"isSignedIn": false}, "NotificationServicesController": {"isCheckingAccountsPresence": false, "isFeatureAnnouncementsEnabled": false, "isFetchingMetamaskNotifications": false, "isMetamaskNotificationsFeatureSeen": false, "isNotificationServicesEnabled": false, "isUpdatingMetamaskNotifications": false, "isUpdatingMetamaskNotificationsAccount": [], "metamaskNotificationsList": [], "metamaskNotificationsReadList": [], "subscriptionAccountsSeen": []}, "NotificationServicesPushController": {"fcmToken": "", "isPushEnabled": true, "isUpdatingFCMToken": false}, "UserStorageController": {"isBackupAndSyncEnabled": true, "isBackupAndSyncUpdateLoading": false, "isAccountSyncingEnabled": true, "isContactSyncingEnabled": true, "isContactSyncingInProgress": false}, "MultichainBalancesController": {"balances": {}}, "RatesController": {"cryptocurrencies": ["btc", "sol"], "fiatCurrency": "usd", "rates": {"btc": {"conversionDate": 0, "conversionRate": 0}, "sol": {"conversionDate": 0, "conversionRate": 0}}}, "MultichainNetworkController": {"isEvmSelected": true, "networksWithTransactionActivity": {}, "selectedMultichainNetworkChainId": "solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp", "multichainNetworkConfigurationsByChainId": {"bip122:000000000019d6689c085ae165831e93": {"chainId": "bip122:000000000019d6689c085ae165831e93", "name": "Bitcoin", "nativeCurrency": "bip122:000000000019d6689c085ae165831e93/slip44:0", "isEvm": false}, "bip122:000000000933ea01ad0ee984209779ba": {"chainId": "bip122:000000000933ea01ad0ee984209779ba", "isEvm": false, "name": "Bitcoin Testnet", "nativeCurrency": "bip122:000000000933ea01ad0ee984209779ba/slip44:0"}, "bip122:00000000da84f2bafbbc53dee25a72ae": {"chainId": "bip122:00000000da84f2bafbbc53dee25a72ae", "isEvm": false, "name": "Bitcoin Testnet4", "nativeCurrency": "bip122:00000000da84f2bafbbc53dee25a72ae/slip44:0"}, "bip122:00000008819873e925422c1ff0f99f7c": {"chainId": "bip122:00000008819873e925422c1ff0f99f7c", "isEvm": false, "name": "Bitcoin Mutinynet", "nativeCurrency": "bip122:00000008819873e925422c1ff0f99f7c/slip44:0"}, "bip122:regtest": {"chainId": "bip122:regtest", "isEvm": false, "name": "Bitcoin Regtest", "nativeCurrency": "bip122:regtest/slip44:0"}, "solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp": {"chainId": "solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp", "name": "Solana", "nativeCurrency": "solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp/slip44:501", "isEvm": false}, "solana:4uhcVJyU9pJkvQyS88uRDiswHXSCkY3z": {"chainId": "solana:4uhcVJyU9pJkvQyS88uRDiswHXSCkY3z", "isEvm": false, "name": "<PERSON>ana <PERSON>net", "nativeCurrency": "solana:4uhcVJyU9pJkvQyS88uRDiswHXSCkY3z/slip44:501"}, "solana:EtWTRABZaYq6iMfeYKouRu166VU2xqa1": {"chainId": "solana:EtWTRABZaYq6iMfeYKouRu166VU2xqa1", "isEvm": false, "name": "<PERSON><PERSON>", "nativeCurrency": "solana:EtWTRABZaYq6iMfeYKouRu166VU2xqa1/slip44:501"}}}, "MultichainTransactionsController": {"nonEvmTransactions": {}}, "MultichainAssetsController": {"accountsAssets": {}, "assetsMetadata": {}}, "BridgeController": {"assetExchangeRates": {}, "minimumBalanceForRentExemptionInLamports": "0", "quoteFetchError": null, "quoteRequest": {"srcTokenAddress": "0x0000000000000000000000000000000000000000"}, "quotes": [], "quotesInitialLoadTime": null, "quotesLastFetched": null, "quotesLoadingStatus": null, "quotesRefreshCount": 0}, "BridgeStatusController": {"txHistory": {}}, "MultichainAssetsRatesController": {"conversionRates": {}, "historicalPrices": {}}, "CronjobController": {"events": {}}, "EarnController": {"lastUpdated": 0, "pooled_staking": {"isEligible": false}, "lending": {"markets": [{"id": "", "chainId": 0, "protocol": "", "name": "", "address": "", "tvlUnderlying": "0", "netSupplyRate": 0, "totalSupplyRate": 0, "underlying": {"address": "", "chainId": 0}, "outputToken": {"address": "", "chainId": 0}, "rewards": [{"token": {"address": "", "chainId": 0}, "rate": 0}]}], "positions": [{"id": "", "chainId": 0, "assets": "0", "marketId": "", "marketAddress": "", "protocol": ""}], "isEligible": false}}, "DeFiPositionsController": {"allDeFiPositions": {}, "allDeFiPositionsCount": {}}, "RewardsController": {"accounts": {}, "activeAccount": null, "activeBoosts": {}, "seasonStatuses": {}, "seasons": {}, "subscriptionReferralDetails": {}, "subscriptions": {}}}